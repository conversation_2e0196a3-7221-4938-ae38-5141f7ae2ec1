#!/bin/bash

# Firewall Configuration Script for Videochat Local Network
# This script opens the necessary ports for local network access

echo "🔥 Configuring Firewall for Videochat Local Network"
echo "=================================================="

# Function to configure UFW (Ubuntu/Debian)
configure_ufw() {
    echo "📋 Configuring UFW firewall..."
    
    # Enable UFW if not already enabled
    sudo ufw --force enable
    
    # Main application port
    sudo ufw allow 8081/tcp comment "Videochat Main Application"
    
    # Traefik dashboard (optional)
    sudo ufw allow 8080/tcp comment "Traefik Dashboard"
    
    # LiveKit WebRTC ports
    sudo ufw allow 7880/tcp comment "LiveKit HTTP API"
    sudo ufw allow 7881/tcp comment "LiveKit TCP RTC"
    sudo ufw allow 7882/udp comment "LiveKit UDP RTC"
    
    # Administrative interfaces (optional - comment out if not needed)
    sudo ufw allow 39001/tcp comment "MinIO Console"
    sudo ufw allow 35672/tcp comment "RabbitMQ Management"
    sudo ufw allow 36686/tcp comment "Jaeger UI"
    sudo ufw allow 5601/tcp comment "OpenSearch Dashboards"
    
    # Database ports (optional - only if external access needed)
    # sudo ufw allow 35432/tcp comment "PostgreSQL"
    # sudo ufw allow 36379/tcp comment "Redis"
    
    echo "✅ UFW firewall configured"
    sudo ufw status
}

# Function to configure firewalld (RHEL/CentOS/Fedora)
configure_firewalld() {
    echo "📋 Configuring firewalld..."
    
    # Enable firewalld if not already enabled
    sudo systemctl enable firewalld
    sudo systemctl start firewalld
    
    # Main application port
    sudo firewall-cmd --permanent --add-port=8081/tcp --add-rich-rule="rule family='ipv4' port protocol='tcp' port='8081' accept"
    
    # Traefik dashboard (optional)
    sudo firewall-cmd --permanent --add-port=8080/tcp
    
    # LiveKit WebRTC ports
    sudo firewall-cmd --permanent --add-port=7880/tcp
    sudo firewall-cmd --permanent --add-port=7881/tcp
    sudo firewall-cmd --permanent --add-port=7882/udp
    
    # Administrative interfaces (optional)
    sudo firewall-cmd --permanent --add-port=39001/tcp
    sudo firewall-cmd --permanent --add-port=35672/tcp
    sudo firewall-cmd --permanent --add-port=36686/tcp
    sudo firewall-cmd --permanent --add-port=5601/tcp
    
    # Reload firewall
    sudo firewall-cmd --reload
    
    echo "✅ firewalld configured"
    sudo firewall-cmd --list-all
}

# Function to configure iptables (generic Linux)
configure_iptables() {
    echo "📋 Configuring iptables..."
    
    # Main application port
    sudo iptables -A INPUT -p tcp --dport 8081 -j ACCEPT
    
    # Traefik dashboard (optional)
    sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
    
    # LiveKit WebRTC ports
    sudo iptables -A INPUT -p tcp --dport 7880 -j ACCEPT
    sudo iptables -A INPUT -p tcp --dport 7881 -j ACCEPT
    sudo iptables -A INPUT -p udp --dport 7882 -j ACCEPT
    
    # Administrative interfaces (optional)
    sudo iptables -A INPUT -p tcp --dport 39001 -j ACCEPT
    sudo iptables -A INPUT -p tcp --dport 35672 -j ACCEPT
    sudo iptables -A INPUT -p tcp --dport 36686 -j ACCEPT
    sudo iptables -A INPUT -p tcp --dport 5601 -j ACCEPT
    
    # Save iptables rules
    if command -v iptables-save >/dev/null 2>&1; then
        sudo iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
    fi
    
    echo "✅ iptables configured"
}

# Detect and configure appropriate firewall
if command -v ufw >/dev/null 2>&1; then
    configure_ufw
elif command -v firewall-cmd >/dev/null 2>&1; then
    configure_firewalld
elif command -v iptables >/dev/null 2>&1; then
    configure_iptables
else
    echo "❌ No supported firewall found (ufw, firewalld, or iptables)"
    echo "Please configure your firewall manually to allow the following ports:"
    echo "  - 8081/tcp (Main Application)"
    echo "  - 8080/tcp (Traefik Dashboard - optional)"
    echo "  - 7880/tcp, 7881/tcp, 7882/udp (LiveKit WebRTC)"
    echo "  - 39001/tcp (MinIO Console - optional)"
    echo "  - 35672/tcp (RabbitMQ Management - optional)"
    echo "  - 36686/tcp (Jaeger UI - optional)"
    echo "  - 5601/tcp (OpenSearch Dashboards - optional)"
    exit 1
fi

echo ""
echo "🎉 Firewall configuration complete!"
echo "The following ports are now open for local network access:"
echo "  ✅ 8081 - Main Videochat Application"
echo "  ✅ 8080 - Traefik Dashboard (optional)"
echo "  ✅ 7880, 7881, 7882 - LiveKit WebRTC"
echo "  ✅ Administrative interfaces (optional)"
echo ""
echo "🔒 Security Note:"
echo "These ports are open to your local network only."
echo "Ensure your router/firewall blocks external access to these ports."
