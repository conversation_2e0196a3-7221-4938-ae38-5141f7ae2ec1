# Common middlewares for local network deployment
# This version removes HTTPS redirects and external dependencies

http:
  middlewares:
    # Retry middleware - useful for handling temporary connection issues
    retry-middleware:
      retry:
        attempts: 4
    
    # Authentication middleware - essential for user authentication
    auth-middleware:
      forwardAuth:
        address: "http://aaa:8060/internal/profile/auth"
        headerField: "X-Auth-UserId"
        authRequestHeaders:
          - "Accept"
          - "<PERSON><PERSON>"
          - "uber-trace-id"
        authResponseHeadersRegex: "^X-Auth-"
    
    # HTTPS redirect middleware - DISABLED for local network
    # redirect-to-https:
    #   redirectScheme:
    #     scheme: https

{% if old_domain is defined and not (local_network_mode | default(false)) %}
    # Old domain redirect - only for cloud deployment
    redirect-from-old-blog-to-public-blog-post:
      redirectRegex:
        regex: "^http.*://{{ old_domain }}/(.*)"
        replacement: "http://{{ local_server_ip | default(domain) }}:8081/public/blog/${1}"
{% endif %}

    # Blog post redirect - modified for local network HTTP
    redirect-from-old-frontend-to-public-blog-post:
      redirectRegex:
        regex: "^http.*://{{ local_server_ip | default(domain) }}:8081/blog/post/(.*)"
        replacement: "http://{{ local_server_ip | default(domain) }}:8081/public/blog/post/${1}"

    # Additional local network middlewares
    
    # CORS middleware for local network development
    cors-middleware:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        accessControlAllowOriginList:
          - "http://{{ local_server_ip | default('localhost') }}:8081"
          - "http://localhost:8081"
          - "http://127.0.0.1:8081"
        accessControlAllowHeaders:
          - "*"
        accessControlAllowCredentials: true
    
    # Security headers middleware - adapted for local network
    security-headers:
      headers:
        # Remove strict security headers that might cause issues in local network
        customRequestHeaders:
          X-Forwarded-Proto: "http"
        customResponseHeaders:
          X-Frame-Options: "SAMEORIGIN"
          X-Content-Type-Options: "nosniff"
          # Remove HSTS and other HTTPS-only headers for local network
    
    # Rate limiting middleware for local network
    rate-limit:
      rateLimit:
        average: 100
        burst: 200
        period: "1m"
    
    # Compression middleware
    compression:
      compress: {}
