# Local network configuration for videochat
# This file overrides settings for local network deployment
# Usage: docker compose -f docker-compose.yml -f docker-compose.local.yml up -d

version: '3.7'

services:
  # Override Traefik to bind to all interfaces for local network access
  traefik:
    ports:
      - 0.0.0.0:8081:8081  # Main application port
      - 0.0.0.0:8080:8080  # Traefik dashboard (optional)
    environment:
      - OTEL_PROPAGATORS=jaeger
      # Disable external connectivity checks
      - TRAEFIK_GLOBAL_CHECKNEWVERSION=false
      - TRAEFIK_GLOBAL_SENDANONYMOUSUSAGE=false

  # Override LiveKit for local network WebRTC
  livekit:
    image: livekit/livekit-server:v1.9.0
    command: --config /etc/livekit.yaml
    restart: unless-stopped
    ports:
      - "0.0.0.0:7880:7880"  # HTTP API
      - "0.0.0.0:7881:7881"  # TCP RTC
      - "0.0.0.0:7882:7882/udp"  # UDP RTC
    networks:
      - backend
    volumes:
      - ./docker/livekit/livekit-local.yaml:/etc/livekit.yaml
    environment:
      - LIVEKIT_CONFIG_FILE=/etc/livekit.yaml

  # Ensure all database ports are accessible if needed for administration
  postgresql:
    ports:
      - 0.0.0.0:35432:5432

  postgresql-citus-coordinator-1:
    ports:
      - 0.0.0.0:45401:5432

  postgresql-citus-worker-1:
    ports:
      - 0.0.0.0:45501:5432

  postgresql-citus-worker-2:
    ports:
      - 0.0.0.0:45502:5432

  # Redis accessible for monitoring
  redis:
    ports:
      - 0.0.0.0:36379:6379

  # MinIO accessible for file management
  minio:
    ports:
      - 0.0.0.0:39000:9000
      - 0.0.0.0:39001:9001
    environment:
      - MINIO_ROOT_USER=AKIAIOSFODNN7EXAMPLE
      - MINIO_ROOT_PASSWORD=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
      - MINIO_NOTIFY_AMQP_ENABLE_primary=on
      - MINIO_NOTIFY_AMQP_URL_primary=amqp://videoChat:videoChatPazZw0rd@rabbitmq:5672
      - MINIO_NOTIFY_AMQP_EXCHANGE_primary=minio-events
      - MINIO_NOTIFY_AMQP_EXCHANGE_TYPE_primary=direct
      - MINIO_NOTIFY_AMQP_MANDATORY_primary=off
      - MINIO_NOTIFY_AMQP_DURABLE_primary=on
      - MINIO_NOTIFY_AMQP_NO_WAIT_primary=off
      - MINIO_NOTIFY_AMQP_AUTO_DELETED_primary=off
      - MINIO_NOTIFY_AMQP_DELIVERY_MODE_primary=2
      - MINIO_PROMETHEUS_AUTH_TYPE=public
      # Remove external redirect URL for local network
      - MINIO_BROWSER_REDIRECT_URL=

  # RabbitMQ management interface
  rabbitmq:
    ports:
      - 0.0.0.0:35672:15672
      - 0.0.0.0:36672:5672

  # Jaeger UI
  jaeger:
    ports:
      - 0.0.0.0:34318:4318
      - 0.0.0.0:34317:4317
      - 0.0.0.0:36686:16686

  # OpenSearch
  opensearch:
    ports:
      - 0.0.0.0:9200:9200

  # OpenSearch Dashboards
  dashboards:
    ports:
      - 0.0.0.0:5601:5601

networks:
  backend:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.host_binding_ipv4: "0.0.0.0"
    ipam:
      driver: default
      config:
        - subnet: **********/24
