logging:
  level:
    root: INFO
    org.springframework.test.web.servlet.result: WARN
    org.springframework.boot.test.context.SpringBootTestContextBootstrapper: WARN
    org.springframework.test.context.transaction.TransactionContext: WARN
    org.springframework.test.context.support.AbstractContextLoader: WARN
    org.springframework.security: WARN
    org.mockserver: WARN
    org.htmlunit: ERROR
    # org.springframework.web: TRACE
#  pattern:
#    console: "%d{dd.MM.yyyy HH:mm:ss.SSS} %-5level @aaa [%thread] %logger{36}:%line %msg%n"

custom.api-url: "http://localhost:${server.port}/api/aaa"
custom.frontend-url: "http://localhost:9080"
custom.template-engine-url-prefix: "http://localhost:${server.port}"
custom.registration-confirm-exit-token-not-found-url: "${custom.frontend-url}/confirm/registration/token-not-found"
custom.registration-confirm-exit-user-not-found-url: "${custom.frontend-url}/confirm/registration/user-not-found"
custom.registration-confirm-exit-success-url: "${custom.frontend-url}"
custom.password-reset-enter-new-url: "${custom.frontend-url}/password-restore/enter-new"
custom.confirm-change-email-exit-success-url: "${custom.frontend-url}"
custom.confirm-change-email-exit-token-not-found-url: "${custom.frontend-url}/confirm/change/email/token-not-found"

# https://docs.spring.io/spring-boot/docs/current/reference/html/boot-features-email.html
# https://yandex.ru/support/mail-new/mail-clients.html
# https://stackoverflow.com/questions/411331/using-javamail-with-tls
spring.mail:
  host: 127.0.0.1
  port: 30025
  username: testEmailUsername
  password: testEmailPassword
  properties:
    # mail.smtp.starttls.enable: "true"
    # mail.smtp.ssl.enable: "true"
    mail.smtp.connectiontimeout: 5000
    mail.smtp.timeout: 3000
    mail.smtp.writetimeout: 5000


spring.session.timeout: 2d
spring.session.redis.repository-type: indexed
cookie-max-age: 60d

spring:
  application:
    name: aaa
  data:
    ldap:
      repositories:
        enabled: false

custom:
  login-properties:
    skip-characters-validation: false
    additional-allowed-characters: []
  email:
    from: <EMAIL>
  confirmation:
    registration:
      token:
        ttl: 20m
    change-email:
      token:
        ttl: 20m
  password-reset:
    token:
      ttl: 20m
  online-estimation: 10m
  # an interval should be less than online-estimation
  frontend-session-ping-interval: 1m
  schedulers:
    user-online:
      enabled: false
      batch-size: 20
      cron: "*/20 * * * * *"
      expiration: "30m"
    sync-ldap:
      enabled: false
      batch-size: 20
      sync-roles: true
      cron: "*/20 * * * * *"
      expiration: "30m"
    sync-keycloak:
      enabled: false
      sync-email-verified: false
      batch-size: 20
      sync-roles: true
      cron: "*/20 * * * * *"
      expiration: "30m"
    await-for-termination: "5m"
    pool-size: 5
  debug-response: false

server.port: 9080

server.tomcat.basedir: ${java.io.tmpdir}/name.nkonev.aaa.tomcat
spring.servlet.multipart.max-file-size: 6MB
spring.servlet.multipart.max-request-size: 8MB
server.servlet.encoding.force-response: true
server.servlet.session.cookie.name: VIDEOCHAT_SESSION
# in order not to remove cookie on mobile browser unloading
server.servlet.session.cookie.max-age: ${cookie-max-age}

custom.csrf.cookie:
  name: VIDEOCHAT_XSRF_TOKEN
  same-site: ""
  max-age: ${cookie-max-age}

custom.it.user: admin
custom.it.password: admin
custom.it.user.id: 1

custom.htmlunit.implicitly-wait-timeout: 10000
custom.htmlunit.window-height: 900
custom.htmlunit.window-width: 1600

spring.datasource:
    name: aaa_ds
    type: org.apache.tomcat.jdbc.pool.DataSource
    # https://jdbc.postgresql.org/documentation/head/connect.html#connection-parameters
    url: ************************************************************************
    username: aaa
    password: "aaaPazZw0rd"
    driverClassName: org.postgresql.Driver
    # https://docs.spring.io/spring-boot/docs/2.0.0.M7/reference/htmlsingle/#boot-features-connect-to-production-database
    # https://tomcat.apache.org/tomcat-8.5-doc/jdbc-pool.html#Common_Attributes
    # https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/#boot-features-connect-to-production-database
    tomcat:
      minIdle: 4
      maxIdle: 8
      maxActive: 10
      maxWait: 60000
      testOnBorrow: true
      testOnConnect: true
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 5000
      validationQuery: SELECT 1;
      validationQueryTimeout: 4
      logValidationErrors: true

spring.liquibase:
  change-log: classpath:/db/changelog-test.yml
  drop-first: true
  analytics-enabled: false
  parameters:
    # bcrypt('admin', 10)
    admin.password: "$2a$10$HsyFGy9IO//nJZxYc2xjDeV/kF7koiPrgIDzPOfgmngKVe9cOyOS2"

spring.data.redis.url: redis://127.0.0.1:36379/0

spring.rabbitmq:
  addresses: localhost:36672
  username: videoChat
  password: videoChatPazZw0rd
  # https://www.javainuse.com/messaging/rabbitmq/error
  listener:
    simple:
      retry:
        enabled: true
        max-attempts: 3
        multiplier: 1.1

management.endpoints.web.exposure.include: '*'
management.endpoint.health.show-details: always
management.tracing.sampling.probability: 1.0
management.otlp.tracing.endpoint: http://localhost:34318/v1/traces
management:
  health:
    mail:
      enabled: false
  server:
    port: 3006
    ssl:
      enabled: false
    add-application-context-header: false

# https://docs.spring.io/spring-boot/reference/data/nosql.html#data.nosql.ldap.embedded
# only for tests
spring.ldap.embedded:
  base-dn: dc=springframework,dc=org
  ldif: classpath:test-server.ldif
  validation:
    enabled: false

custom.role-mappings:
  ldap:
    - their: "user"
      our: "ROLE_USER"
    - their: "admin"
      our: "ROLE_ADMIN"

custom.ldap:
  auth:
    base: "ou=people,dc=springframework,dc=org"
    enabled: true
    filter: "cn={0}"
  group:
    base: "ou=groups,dc=springframework,dc=org"
    filter: "cn={0}"
  attribute-names:
    id: uid # name of attribute, which is considered as ldap_id. any id-like attribute, which won't be changed on user rename, it can be number or string
    role: uniqueMember
    email: mail
    locked: blocked
    username: cn
  password:
    encodingType: ""
    strength: 10
  forbid-user-account-change: true

custom.keycloak:
#  resolve-conflicts-strategy: WRITE_NEW_AND_REMOVE_OLD
  token-delta: 5s
  allow-unbind: true
  forbid-user-account-change: true

custom.vkontakte:
  resolve-conflicts-strategy: WRITE_NEW_AND_RENAME_OLD

custom.google:
  resolve-conflicts-strategy: WRITE_NEW_AND_RENAME_OLD

custom.facebook:
  resolve-conflicts-strategy: WRITE_NEW_AND_RENAME_OLD

custom.http-client:
  connect-timeout: 3s
  read-timeout: 30s

custom.allowed-avatar-urls: ""

custom.admins-corner:
  management-urls:
    - "/jaeger"
    - "/minio"
    - "/rabbitmq"
    - "/postgresql"
    - "/opensearch-dashboards"
