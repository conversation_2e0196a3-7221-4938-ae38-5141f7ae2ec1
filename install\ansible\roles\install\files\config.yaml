# https://raw.githubusercontent.com/jaegertracing/jaeger/refs/tags/v2.7.0/cmd/jaeger/internal/all-in-one.yaml
service:
  extensions: [jaeger_storage, jaeger_query]
  # extensions: [jaeger_storage, jaeger_query, remote_sampling, healthcheckv2, expvar, zpages]
  pipelines:
    traces:
      receivers: [otlp, jaeger, zipkin]
      processors: [batch]
      exporters: [jaeger_storage_exporter]
  telemetry:
#    resource:
#      service.name: jaeger
#    metrics:
#      level: detailed
#      readers:
#        - pull:
#            exporter:
#              prometheus:
#                host: "${env:JAEGER_LISTEN_HOST:-localhost}"
#                port: 8888
    logs:
      level: info
    # TODO Initialize telemetry tracer once OTEL released new feature.
    # https://github.com/open-telemetry/opentelemetry-collector/issues/10663

extensions:
  jaeger_query:
    storage:
      traces: some_storage
    # https://www.jaegertracing.io/docs/2.7/deployment/configuration/#jaeger-query
    base_path: /jaeger

  jaeger_storage:
    backends:
      some_storage:
        # https://raw.githubusercontent.com/jaegertracing/jaeger/refs/tags/v2.7.0/cmd/jaeger/config-opensearch.yaml
        opensearch:
          server_urls:
            - http://opensearch:9200
          indices:
            index_prefix: "jaeger-main"
            spans:
              date_layout: "2006.01.02"
#              rollover_frequency: "day"
#              shards: 5
#              replicas: 1
            services:
              date_layout: "2006.01.02"
#              rollover_frequency: "day"
#              shards: 5
#              replicas: 1
            dependencies:
              date_layout: "2006.01.02"
#              rollover_frequency: "day"
#              shards: 5
#              replicas: 1
            sampling:
              date_layout: "2006.01.02"
#              rollover_frequency: "day"
#              shards: 5
#              replicas: 1
#  remote_sampling:
#    # We can either use file or adaptive sampling strategy in remote_sampling
#    file:
#      path:
#      default_sampling_probability: 1
#      reload_interval: 1s
#    # adaptive:
#    #   sampling_store: some_store
#    #   initial_sampling_probability: 0.1
#    http:
#      endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:5778"
#    grpc:
#      endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:5779"
#
#  healthcheckv2:
#    use_v2: true
#    http:
#      endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:13133"
#    grpc:
#
#  expvar:
#    endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:27777"
#
#  zpages:
#    # for some reason the official extension listens on ephemeral port 55679
#    # so we override it with a normal port
#    endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:27778"

receivers:
  otlp:
    protocols:
      grpc:
        endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:4317"
      http:
        endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:4318"

  jaeger:
    protocols:
      grpc:
        endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:14250"
      thrift_http:
        endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:14268"
      thrift_binary:
        endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:6832"
      thrift_compact:
        endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:6831"

  zipkin:
    endpoint: "${env:JAEGER_LISTEN_HOST:-localhost}:9411"

processors:
  batch:

exporters:
  jaeger_storage_exporter:
    trace_storage: some_storage
