# Local Network Configuration Guide for main.yml

This guide shows exactly what to add and comment out in your `install/ansible/roles/install/vars/main.yml` file for local network deployment.

## 🔧 **Step 1: <PERSON><PERSON> and Rename the Example File**

```bash
cd install/ansible/roles/install/vars/
cp main.yml.example main.yml
```

## 📝 **Step 2: Required Modifications**

### **🚫 COMMENT OUT (Disable for Local Network)**

#### **1. Docker Proxy Settings**
```yaml
# Comment out - not needed for local network
# docker_proxy: "socks5://proxy.huecker.io:443"
```

#### **2. HTTPS/TLS Configuration**
```yaml
# Comment out - no HTTPS for local network
# acme_email: "<EMAIL>"
# old_domain: "old.example.com"
# domain: "example.com"
```

#### **3. Email Configuration**
```yaml
# Comment out - no email for local network
# mail_host: "smtp.example.com"
# mail_port: "465"
# mail_from: "<EMAIL>"
# mail_username: "email_user"
# mail_password: "email_password"
```

#### **4. OAuth 2.0 Providers**
```yaml
# Comment out - no external OAuth for local network
# vkontakte_client_id: "6805077"
# vkontakte_client_secret: "your-app-client-secret"
# google_client_id: "987654321-d12sja.apps.googleusercontent.com"
# google_client_secret: "your-app-client-secret"
# facebook_client_id: "333333333333333"
# facebook_client_secret: "your-app-client-secret"
```

#### **5. GitHub Actions SSH Key**
```yaml
# Comment out - not needed for local network
# github_actions_ssh_public_key: "ssh-rsa AAABBBCCC..."
```

### **✅ ADD (Required for Local Network)**

#### **1. Deployment Mode Setting**
```yaml
# ADD at the top of the file
deployment_mode: "local"
```

#### **2. Local Server IP Configuration**
```yaml
# ADD - Replace with your actual server IP
local_server_ip: "*************"  # CHANGE THIS to your server's IP
```

#### **3. LiveKit Local Network Settings**
```yaml
# ADD these LiveKit settings for local network
livekit_use_external_ip: false
livekit_ice_lite: false
livekit_turn_enabled: false
livekit_development_mode: true
livekit_bind_all_interfaces: true
livekit_disable_external_stun: true
```

#### **4. Local Network Mode Settings**
```yaml
# ADD these local network settings
local_network_mode: true
bind_all_interfaces: true
use_http_only: true
disable_external_dependencies: true
service_bind_address: "0.0.0.0"
```

#### **5. Firewall Ports Configuration**
```yaml
# ADD firewall ports for local network access
local_network_ports:
  - "8081/tcp"    # Main application
  - "8080/tcp"    # Traefik dashboard
  - "7880/tcp"    # LiveKit HTTP
  - "7881/tcp"    # LiveKit TCP RTC
  - "7882/udp"    # LiveKit UDP RTC (CRITICAL for video calls)
  - "39000/tcp"   # MinIO API
  - "39001/tcp"   # MinIO Console
  - "35672/tcp"   # RabbitMQ Management
  - "36672/tcp"   # RabbitMQ AMQP
  - "36379/tcp"   # Redis
  - "35432/tcp"   # PostgreSQL
  - "36686/tcp"   # Jaeger UI
  - "34318/tcp"   # Jaeger OTLP HTTP
  - "34317/tcp"   # Jaeger OTLP GRPC
  - "38080/tcp"   # PgAdmin
  - "9200/tcp"    # OpenSearch
  - "5601/tcp"    # OpenSearch Dashboards
```

#### **6. Database Configuration**
```yaml
# ADD/MODIFY database settings
postgresql_password: "postgresqlPassword"
chat_db_password: "chatPazZw0rd"
redis_password: ""  # No password for local network
rabbitmq_user: "videoChat"
rabbitmq_password: "videoChatPazZw0rd"
```

#### **7. SSL/TLS Disabled**
```yaml
# ADD - disable SSL for local network
ssl_enabled: false
letsencrypt_enabled: false
use_https: false
```

#### **8. External Integrations Disabled**
```yaml
# ADD - disable external integrations
ldap_enabled: false
external_auth_enabled: false
oauth2_enabled: false
email_notifications_enabled: false
```

#### **9. Development Settings**
```yaml
# ADD - enable debugging for local network
debug_mode: true
log_level: "DEBUG"
enable_tracing: true
enable_metrics: true
```

### **🔄 MODIFY (Change Values for Local Network)**

#### **1. MinIO Credentials**
```yaml
# CHANGE from AWS-style to simple credentials
minio_user: "admin"  # Instead of "AKIAIOSFODNN7EXAMPLE"
minio_password: "password123"  # Instead of complex AWS key
```

#### **2. LiveKit Settings**
```yaml
# CHANGE to simple values for local network
livekit_max_participants_per_room: "50"  # Instead of "100"
livekit_api_key: "devkey"  # Instead of complex key
livekit_api_secret: "secret"  # Instead of complex secret
```

#### **3. Log Retention (Reduce for Local Network)**
```yaml
# CHANGE to shorter retention for local network
opensearch_log_retention: "7d"  # Instead of "14d"
opensearch_span_retention: "3d"  # Instead of "7d"
opensearch_service_retention: "7d"  # Instead of "14d"
```

## 🎯 **Step 3: Critical Settings Summary**

### **Most Important Settings for Video Calls to Work:**

1. **Server IP Address** (MUST CHANGE):
   ```yaml
   local_server_ip: "YOUR_ACTUAL_SERVER_IP"
   ```

2. **LiveKit Local Network Mode** (MUST ADD):
   ```yaml
   livekit_use_external_ip: false
   livekit_turn_enabled: false
   livekit_disable_external_stun: true
   ```

3. **UDP Port 7882** (CRITICAL for WebRTC):
   ```yaml
   local_network_ports:
     - "7882/udp"    # This port is ESSENTIAL for video calls
   ```

4. **Deployment Mode** (MUST ADD):
   ```yaml
   deployment_mode: "local"
   ```

## ⚠️ **Common Mistakes to Avoid**

1. **Don't forget to change `local_server_ip`** to your actual server's IP address
2. **Don't leave OAuth providers enabled** - they require internet access
3. **Don't enable HTTPS/TLS** - it requires Let's Encrypt (internet access)
4. **Don't forget UDP port 7882** - video calls won't work without it
5. **Don't use complex passwords in development** - keep it simple for local testing

## 🔍 **Verification Checklist**

After modifying your `main.yml` file, verify:

- [ ] `deployment_mode: "local"` is set
- [ ] `local_server_ip` is set to your server's actual IP
- [ ] All OAuth providers are commented out
- [ ] Email configuration is commented out
- [ ] HTTPS/TLS settings are commented out or disabled
- [ ] LiveKit local network settings are added
- [ ] UDP port 7882 is in the firewall ports list
- [ ] MinIO credentials are simplified
- [ ] Database passwords are set

## 🚀 **Next Steps**

After configuring `main.yml`:

1. **Create inventory file**:
   ```bash
   echo "[videochat]" > inventory
   echo "YOUR_SERVER_IP ansible_user=YOUR_USERNAME" >> inventory
   ```

2. **Run the local network deployment**:
   ```bash
   ansible-playbook -i inventory playbook-local-network.yaml --ask-become-pass
   ```

3. **Access the application**:
   ```
   http://YOUR_SERVER_IP:8081
   ```

This configuration will ensure your videochat deployment works properly in a local network environment without internet access, with all LiveKit/WebRTC issues resolved.
