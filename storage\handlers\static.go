package handlers

import (
	"embed"
	"github.com/labstack/echo/v4"
	"io/fs"
	"net/http"
	"nkonev.name/storage/logger"
	"strings"
)

//go:embed static
var embeddedFiles embed.FS

type StaticMiddleware echo.MiddlewareFunc

func ConfigureStaticMiddleware(lgr *logger.Logger) StaticMiddleware {
	fsys, err := fs.Sub(embeddedFiles, "static")
	if err != nil {
		lgr.Panicf("Cannot open static embedded dir")
	}
	staticDir := http.FS(fsys)

	h := http.FileServer(staticDir)
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			reqUrl := c.Request().RequestURI
			if reqUrl == "/" || reqUrl == "/index.html" || reqUrl == "/favicon.ico" || strings.HasPrefix(reqUrl, "/build") || reqUrl == "/git.json" {
				h.Serve<PERSON>TP(c.Response().<PERSON>, c.Request())
				return nil
			} else {
				return next(c)
			}
		}
	}
}
