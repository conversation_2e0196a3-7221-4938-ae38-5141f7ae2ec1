#!/bin/bash

# Videochat Local Network Deployment Script
# This script deploys videochat for local network operation without internet access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INVENTORY_FILE="${SCRIPT_DIR}/inventory"
VARS_FILE="${SCRIPT_DIR}/roles/install/vars/main.yml"
LOCAL_VARS_EXAMPLE="${SCRIPT_DIR}/roles/install/vars/local-network.yml.example"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}Videochat Local Network Deployment${NC}"
echo -e "${BLUE}========================================${NC}"

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}Error: This script should not be run as root${NC}"
   echo "Please run as a regular user with sudo privileges"
   exit 1
fi

# Check if Ansible is installed
if ! command -v ansible-playbook &> /dev/null; then
    echo -e "${RED}Error: Ansible is not installed${NC}"
    echo "Please install Ansible first:"
    echo "  sudo dnf install ansible"  # For RHEL/CentOS/Rocky
    echo "  sudo apt install ansible"  # For Ubuntu/Debian
    exit 1
fi

# Check if inventory file exists
if [[ ! -f "$INVENTORY_FILE" ]]; then
    echo -e "${YELLOW}Creating inventory file...${NC}"
    cat > "$INVENTORY_FILE" << EOF
[videochat]
localhost ansible_connection=local

[videochat:vars]
ansible_user=$(whoami)
ansible_become=yes
ansible_become_method=sudo
EOF
    echo -e "${GREEN}Created inventory file: $INVENTORY_FILE${NC}"
fi

# Check if variables file exists
if [[ ! -f "$VARS_FILE" ]]; then
    if [[ -f "$LOCAL_VARS_EXAMPLE" ]]; then
        echo -e "${YELLOW}Creating variables file from local network example...${NC}"
        cp "$LOCAL_VARS_EXAMPLE" "$VARS_FILE"
        echo -e "${GREEN}Created variables file: $VARS_FILE${NC}"
        echo -e "${YELLOW}Please review and customize the variables in: $VARS_FILE${NC}"
    else
        echo -e "${RED}Error: Variables file not found and no example available${NC}"
        echo "Please create: $VARS_FILE"
        exit 1
    fi
fi

# Display current network configuration
echo -e "${BLUE}Current Network Configuration:${NC}"
IP_ADDRESS=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' || echo "Unable to detect")
echo "  IP Address: $IP_ADDRESS"
echo "  Hostname: $(hostname)"
echo

# Confirm deployment
echo -e "${YELLOW}This will deploy videochat for local network operation.${NC}"
echo -e "${YELLOW}The following features will be DISABLED:${NC}"
echo "  - HTTPS/TLS certificates"
echo "  - External STUN/TURN servers"
echo "  - External OAuth providers"
echo "  - Let's Encrypt certificates"
echo "  - External API integrations"
echo
echo -e "${YELLOW}Services will be accessible at:${NC}"
echo "  - Main Application: http://$IP_ADDRESS:8081"
echo "  - Traefik Dashboard: http://$IP_ADDRESS:8080"
echo "  - MinIO Console: http://$IP_ADDRESS:39001"
echo "  - RabbitMQ Management: http://$IP_ADDRESS:35672"
echo "  - Jaeger UI: http://$IP_ADDRESS:36686"
echo

read -p "Do you want to continue with local network deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Deployment cancelled.${NC}"
    exit 0
fi

# Check available disk space
echo -e "${BLUE}Checking system requirements...${NC}"
AVAILABLE_SPACE=$(df / | awk 'NR==2 {print $4}')
REQUIRED_SPACE=5242880  # 5GB in KB
if [[ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]]; then
    echo -e "${RED}Warning: Low disk space detected${NC}"
    echo "Available: $(($AVAILABLE_SPACE / 1024 / 1024))GB, Recommended: 5GB+"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check if Docker is running (if installed)
if command -v docker &> /dev/null; then
    if ! docker info &> /dev/null; then
        echo -e "${YELLOW}Docker is installed but not running. The playbook will start it.${NC}"
    fi
fi

# Run the Ansible playbook
echo -e "${BLUE}Starting deployment...${NC}"
echo "This may take 10-20 minutes depending on your internet connection and system performance."
echo

# Set deployment mode environment variable
export DEPLOYMENT_MODE="local"

# Run the local network playbook
if ansible-playbook \
    -i "$INVENTORY_FILE" \
    "$SCRIPT_DIR/playbook-local-network.yaml" \
    --extra-vars "deployment_mode=local" \
    --ask-become-pass; then
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}DEPLOYMENT SUCCESSFUL!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo
    echo -e "${GREEN}Videochat is now running on your local network.${NC}"
    echo
    echo -e "${BLUE}Access URLs:${NC}"
    echo "  Main Application: http://$IP_ADDRESS:8081"
    echo "  Traefik Dashboard: http://$IP_ADDRESS:8080"
    echo "  MinIO Console: http://$IP_ADDRESS:39001"
    echo "  RabbitMQ Management: http://$IP_ADDRESS:35672"
    echo "  Jaeger UI: http://$IP_ADDRESS:36686"
    echo
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Open your browser and go to: http://$IP_ADDRESS:8081"
    echo "2. Create an admin account"
    echo "3. Start using videochat!"
    echo
    echo -e "${BLUE}Troubleshooting:${NC}"
    echo "- Check service status: docker ps"
    echo "- View logs: docker logs <container_name>"
    echo "- Configuration details: /opt/videochat/LOCAL_NETWORK_DEPLOYMENT.md"
    echo
    echo -e "${YELLOW}Note: Make sure other devices on your network can access port 8081${NC}"
    echo -e "${YELLOW}and UDP port 7882 for video calls to work properly.${NC}"
    
else
    echo -e "${RED}========================================${NC}"
    echo -e "${RED}DEPLOYMENT FAILED!${NC}"
    echo -e "${RED}========================================${NC}"
    echo
    echo -e "${RED}The deployment encountered an error.${NC}"
    echo
    echo -e "${BLUE}Troubleshooting steps:${NC}"
    echo "1. Check the error messages above"
    echo "2. Verify your variables file: $VARS_FILE"
    echo "3. Check system requirements (disk space, memory)"
    echo "4. Ensure you have sudo privileges"
    echo "5. Check network connectivity for Docker image downloads"
    echo
    echo -e "${BLUE}For support:${NC}"
    echo "- Check the project documentation"
    echo "- Review the Ansible playbook logs"
    echo "- Verify firewall settings"
    
    exit 1
fi
