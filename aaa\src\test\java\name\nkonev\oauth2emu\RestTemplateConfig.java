package name.nkonev.oauth2emu;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        RestTemplateBuilder builder = new RestTemplateBuilder();
        return builder
            .connectTimeout(Duration.of(10, ChronoUnit.SECONDS))
            .readTimeout(Duration.of(20, ChronoUnit.SECONDS))
            .requestFactory(JdkClientHttpRequestFactory.class)
            .build();
    }

}
