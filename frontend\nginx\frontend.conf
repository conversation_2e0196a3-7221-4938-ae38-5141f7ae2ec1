# https://stackoverflow.com/questions/17905975/how-to-speed-up-delivery-for-static-files-with-nginx-cache-them-in-memory
# https://docs.nginx.com/nginx/admin-guide/web-server/serving-static-content/
# https://www.cloudpanel.io/blog/nginx-performance/
# https://www.arubacloud.com/tutorial/filter-and-optimize-static-file-requests-with-nginx-on-ubuntu-18-04.aspx

user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections 1024;
    multi_accept on;
    use epoll;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile on;
    sendfile_max_chunk 1m;

    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;

    server_tokens off;

    gzip on;
    gzip_types text/plain text/css application/javascript application/json image/svg+xml;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_comp_level 6;
    gzip_proxied any;

    client_header_buffer_size 1k;

    server {
        listen       8082;
        server_name  localhost;

        location / {
          root   /usr/share/nginx/html/dist;
          index  index.html;

          # 404 fallback
          try_files $uri /index.html;

          expires 365d;
          add_header Cache-Control "public, no-transform";
        }

        location /blog {
          alias   /usr/share/nginx/html/dist;
          index   /blog/index.html;

          # 404 fallback
          try_files /blog/$uri /blog/index.html;

          expires 365d;
          add_header Cache-Control "public, no-transform";
        }
    }

}
