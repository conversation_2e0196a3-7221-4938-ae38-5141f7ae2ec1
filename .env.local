# Local Network Configuration for Videochat
# Replace YOUR_LOCAL_IP with your actual server IP address

# Network Configuration
LOCAL_IP=YOUR_LOCAL_IP
FRONTEND_URL=http://YOUR_LOCAL_IP:8081
CHAT_API_URL=http://YOUR_LOCAL_IP:1235
AAA_API_URL=http://YOUR_LOCAL_IP:8060

# Disable external services
DISABLE_EXTERNAL_SERVICES=true
TRAEFIK_GLOBAL_CHECKNEWVERSION=false
TRAEFIK_GLOBAL_SENDANONYMOUSUSAGE=false

# Database Configuration
POSTGRES_PASSWORD=postgresqlPassword
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432

# Redis Configuration  
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# RabbitMQ Configuration
RABBITMQ_DEFAULT_USER=videoChat
RABBITMQ_DEFAULT_PASS=videoChatPazZw0rd
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

# MinIO Configuration
MINIO_ROOT_USER=AKIAIOSFODNN7EXAMPLE
MINIO_ROOT_PASSWORD=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
MINIO_HOST=minio
MINIO_PORT=9000

# LiveKit Configuration
LIVEKIT_API_KEY=APIznJxWShGW3Kt
LIVEKIT_API_SECRET=KEUUtCDVRqXk9me0Ok94g8G9xwtnjMeUxfNMy8dow6iA
LIVEKIT_HOST=livekit
LIVEKIT_PORT=7880

# Logging Configuration
LOG_LEVEL=info
WRITE_LOG_TO_FILE=true

# Development Mode
NODE_ENV=production
ENVIRONMENT=local
