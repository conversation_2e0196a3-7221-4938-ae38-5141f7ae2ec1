# Local Network Deployment Variables
# Copy this file to main.yml and customize for your local network deployment

# Deployment mode - set to 'local' for local network deployment
deployment_mode: "local"

# Network configuration for local deployment
local_network:
  # Set to true to disable external dependencies
  offline_mode: true
  # Use HTTP instead of HTTPS for local network
  use_http: true
  # Bind services to all interfaces for local network access
  bind_all_interfaces: true

# LiveKit configuration for local network
livekit_api_key: "devkey"
livekit_api_secret: "secret"
livekit_max_participants_per_room: 50

# Disable external services for local network
livekit_config:
  use_external_ip: false
  ice_lite: false
  turn_enabled: false
  webrtc_ice_servers: []
  development_mode: true

# MinIO configuration
minio_user: "admin"
minio_password: "password123"

# Database configuration
postgresql_password: "postgresqlPassword"
chat_db_password: "chatPazZw0rd"

# RabbitMQ configuration
rabbitmq_user: "videoChat"
rabbitmq_password: "videoChatPazZw0rd"

# OAuth2 configuration (optional for local network)
oauth2_providers:
  google:
    enabled: false
  facebook:
    enabled: false
  vkontakte:
    enabled: false
  keycloak:
    enabled: false

# Email configuration (disabled for local network)
email:
  enabled: false

# External integrations (disabled for local network)
integrations:
  ldap:
    enabled: false
  external_auth:
    enabled: false

# SSL/TLS configuration (disabled for local network)
ssl:
  enabled: false
  letsencrypt: false

# Monitoring configuration (optional)
monitoring:
  jaeger:
    enabled: true
  opensearch:
    enabled: true
  prometheus:
    enabled: false

# Image tags
image_install_tag: "latest"
image_continuous_tag: "changing"

# Directory configuration
dir_prefix: "/opt/videochat"

# Docker Swarm configuration
swarm_stack_name: "VIDEOCHATSTACK"

# Service ports for local network access
service_ports:
  traefik_web: 8081
  traefik_dashboard: 8080
  postgresql: 35432
  redis: 36379
  minio_api: 39000
  minio_console: 39001
  rabbitmq_management: 35672
  rabbitmq_amqp: 36672
  livekit_http: 7880
  livekit_tcp: 7881
  livekit_udp: 7882
  jaeger_ui: 36686
  jaeger_otlp_http: 34318
  jaeger_otlp_grpc: 34317
  pgadmin: 38080
  opensearch: 9200
  opensearch_dashboards: 5601

# Local network specific environment variables
local_env_vars:
  # Disable external connectivity checks
  TRAEFIK_GLOBAL_CHECKNEWVERSION: "false"
  TRAEFIK_GLOBAL_SENDANONYMOUSUSAGE: "false"
  # LiveKit local network settings
  LIVEKIT_LOCAL_NETWORK: "true"
  LIVEKIT_DISABLE_EXTERNAL_STUN: "true"
  # Application settings
  CUSTOM_DISABLE_EXTERNAL_APIS: "true"
  CUSTOM_LOCAL_NETWORK_MODE: "true"
