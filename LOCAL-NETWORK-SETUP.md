# Videochat Local Network Setup Guide

This guide will help you configure and run the Videochat application completely on your local network without requiring any external internet access.

## 🎯 Overview

The Videochat application is designed to be self-contained and can run entirely offline. This setup ensures:

- ✅ No external CDN dependencies
- ✅ All assets bundled locally
- ✅ WebRTC works within local network
- ✅ All services communicate internally
- ✅ No internet access required after initial setup

## 🚀 Quick Start

### Prerequisites

1. **Docker** and **Docker Compose** installed
2. **Linux server** (Ubuntu 20.04+, CentOS 8+, or similar)
3. At least **8GB RAM** and **20GB free disk space**
4. Local network with devices you want to connect
5. Root or sudo access for firewall configuration

### Complete Setup Process

1. **Clone/Copy the videochat project to your Linux server**

2. **Run the automated setup:**
```bash
# Make scripts executable
chmod +x setup-local-network.sh
chmod +x configure-firewall.sh

# Configure firewall (run first)
sudo ./configure-firewall.sh

# Setup and start videochat
./setup-local-network.sh
```

3. **Manual IP configuration (if auto-detection fails):**
```bash
# Edit .env.local and replace YOUR_LOCAL_IP with your server's IP
nano .env.local
```

### Manual Setup

If you prefer manual setup or need to customize:

1. **Start the services:**
   ```bash
   docker compose -f docker-compose.yml -f docker-compose.local.yml up -d
   ```

2. **Wait for services to initialize:**
   ```bash
   docker compose -f docker-compose.yml -f docker-compose.local.yml logs -f
   ```

3. **Access the application:**
   - Replace `YOUR_LOCAL_IP` with your actual local IP address
   - Main application: `http://YOUR_LOCAL_IP:8081`

## 🔧 Configuration Details

### Network Configuration

The local network setup includes these key changes:

1. **Docker Network Binding:**
   - Changed from `127.0.0.1` to `0.0.0.0` to allow local network access
   - All services accessible from other devices on the same network

2. **LiveKit WebRTC Configuration:**
   - Disabled external STUN servers
   - Configured for local network operation
   - Uses internal Docker networking

3. **Service Ports:**
   - Main Application: `8081`
   - Traefik Dashboard: `8080`
   - All admin interfaces accessible on local network

### Key Services and Ports

| Service | Port | Purpose |
|---------|------|---------|
| **Main App** | 8081 | Primary videochat application |
| Traefik Dashboard | 8080 | Load balancer admin |
| PostgreSQL | 35432 | Main database |
| Redis | 36379 | Cache and sessions |
| RabbitMQ Management | 35672 | Message queue admin |
| MinIO Console | 39001 | File storage admin |
| Jaeger UI | 36686 | Distributed tracing |
| OpenSearch Dashboards | 5601 | Log analysis |
| LiveKit | 7880 | WebRTC server |

## 📱 Accessing from Other Devices

### Same Network Access

1. **Find your server's IP address:**
   - Windows: `ipconfig`
   - Linux/macOS: `ip addr` or `ifconfig`

2. **Share the URL:**
   - `http://YOUR_SERVER_IP:8081`

3. **Firewall Configuration:**
   - Ensure port 8081 is open on your server
   - Windows: Windows Defender Firewall
   - Linux: `ufw allow 8081`

### Mobile Device Setup

For mobile devices to access video features:

1. **Chrome on Android:**
   - Go to `chrome://flags/#unsafely-treat-insecure-origin-as-secure`
   - Add your server IP: `http://YOUR_SERVER_IP:8081`
   - Restart Chrome

2. **Firefox on Mobile:**
   - Install Firefox Beta
   - Go to `about:config`
   - Set `media.devices.insecure.enabled` to `true`
   - Set `media.getusermedia.insecure.enabled` to `true`

## 🔐 Default Credentials

### Application
- **Admin User:** `admin` / `admin` (created on first run)

### Infrastructure Services
- **MinIO:** `AKIAIOSFODNN7EXAMPLE` / `wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY`
- **RabbitMQ:** `videoChat` / `videoChatPazZw0rd`
- **PostgreSQL:** `postgres` / `postgresqlPassword`

## 🛠️ Management Commands

### Start Services
```bash
docker compose -f docker-compose.yml -f docker-compose.local.yml up -d
```

### Stop Services
```bash
docker compose -f docker-compose.yml -f docker-compose.local.yml down
```

### View Logs
```bash
docker compose -f docker-compose.yml -f docker-compose.local.yml logs -f
```

### Restart Specific Service
```bash
docker compose -f docker-compose.yml -f docker-compose.local.yml restart SERVICE_NAME
```

### Check Service Status
```bash
docker compose -f docker-compose.yml -f docker-compose.local.yml ps
```

## 🔍 Troubleshooting

### Common Issues

1. **Services not accessible from other devices:**
   - Check firewall settings
   - Verify IP address is correct
   - Ensure Docker is binding to 0.0.0.0

2. **Video calls not working:**
   - Check LiveKit logs: `docker compose logs livekit`
   - Verify WebRTC ports are open (7880-7882)
   - Ensure browser allows insecure origins

3. **Database connection issues:**
   - Wait for PostgreSQL to fully initialize
   - Check logs: `docker compose logs postgresql`

4. **File upload/download issues:**
   - Verify MinIO is running: `docker compose logs minio`
   - Check storage permissions

### Log Analysis

View specific service logs:
```bash
# Application services
docker compose logs -f frontend
docker compose logs -f chat
docker compose logs -f video
docker compose logs -f aaa

# Infrastructure
docker compose logs -f postgresql
docker compose logs -f redis
docker compose logs -f rabbitmq
docker compose logs -f livekit
```

## 🔄 Updates and Maintenance

### Updating the Application

1. **Stop services:**
   ```bash
   docker compose -f docker-compose.yml -f docker-compose.local.yml down
   ```

2. **Pull latest images (if internet available):**
   ```bash
   docker compose -f docker-compose.yml -f docker-compose.local.yml pull
   ```

3. **Restart services:**
   ```bash
   docker compose -f docker-compose.yml -f docker-compose.local.yml up -d
   ```

### Backup Data

Important data locations:
- **Database:** PostgreSQL volumes
- **Files:** MinIO volumes
- **Configuration:** Local config files

```bash
# Backup volumes
docker run --rm -v videochat_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .
docker run --rm -v videochat_minio_data:/data -v $(pwd):/backup alpine tar czf /backup/minio_backup.tar.gz -C /data .
```

## 🎉 Success!

Once setup is complete, you'll have a fully functional video conferencing platform running entirely on your local network, accessible by any device connected to the same network without requiring internet access.

The application includes:
- 💬 Real-time chat
- 📹 Video conferencing
- 📁 File sharing
- 👥 User management
- 🔒 Authentication
- 📊 Administrative tools
