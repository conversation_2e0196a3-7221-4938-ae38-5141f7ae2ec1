# Video service configuration for local network deployment
version: '3.7'

services:
  video:
    image: nkonev/chat-video:{{ image_install_tag }}
    hostname: video
    environment:
      - _JAVA_OPTIONS=-Djava.awt.headless=true
      - SPRING_DATASOURCE_URL=**************************************
      - SPRING_DATASOURCE_USERNAME=chat
      - SPRING_DATASOURCE_PASSWORD=chatPazZw0rd
      - SPRING_REDIS_URL=redis://redis:6379/0
      - CUSTOM_STOMP_BROKER_URL=ws://{{ ansible_default_ipv4.address }}:8081/api/chat/stomp
      - CUSTOM_STOMP_BROKER_URL_WS=ws://{{ ansible_default_ipv4.address }}:8081/api/chat/stomp
      - CUSTOM_STOMP_BROKER_URL_WSS=ws://{{ ansible_default_ipv4.address }}:8081/api/chat/stomp
      - CUSTOM_LIVEKIT_URL=ws://{{ ansible_default_ipv4.address }}:7880
      - CUSTOM_LIVEKIT_URL_WS=ws://{{ ansible_default_ipv4.address }}:7880
      - CUSTOM_LIVEKIT_URL_WSS=ws://{{ ansible_default_ipv4.address }}:7880
      - LIVEKIT_API_KEY={{ livekit_api_key }}
      - LIVEKIT_API_SECRET={{ livekit_api_secret }}
      - CUSTOM_FRONTEND_URL=http://{{ ansible_default_ipv4.address }}:8081
      - CUSTOM_FRONTEND_URL_HTTP=http://{{ ansible_default_ipv4.address }}:8081
      - CUSTOM_FRONTEND_URL_HTTPS=http://{{ ansible_default_ipv4.address }}:8081
      - MANAGEMENT_OTLP_TRACING_ENDPOINT=http://jaeger:4318/v1/traces
      - MANAGEMENT_ZIPKIN_TRACING_ENDPOINT=http://jaeger:9411/api/v2/spans
      - LOGGING_LEVEL_NAME_NKONEV_CHAT=DEBUG
      - CUSTOM_RABBITMQ_HOST=rabbitmq
      - CUSTOM_RABBITMQ_PORT=5672
      - CUSTOM_RABBITMQ_USERNAME=videoChat
      - CUSTOM_RABBITMQ_PASSWORD=videoChatPazZw0rd
      - CUSTOM_RABBITMQ_VHOST=/
      - CUSTOM_EGRESS_URL=http://egress:9090
      # Local network specific settings
      - CUSTOM_LIVEKIT_LOCAL_NETWORK=true
      - CUSTOM_DISABLE_EXTERNAL_STUN=true
      - CUSTOM_USE_LOCAL_IP={{ ansible_default_ipv4.address }}
    networks:
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.http.services.video.loadbalancer.server.port=1237"
      - "traefik.http.routers.video.rule=PathPrefix(`/api/video`)"
      - "traefik.http.routers.video.entrypoints=web"
      - "traefik.http.routers.video.service=video"
      # No TLS for local network
      - "traefik.http.routers.video.tls=false"
    logging:
      driver: "journald"
      options:
        tag: chat-video
    volumes:
      - {{ dir_prefix }}/video/config:/config
    depends_on:
      - postgresql
      - redis
      - rabbitmq
      - livekit

networks:
  backend:
    external: true
    name: "{{ swarm_stack_name }}_backend"
