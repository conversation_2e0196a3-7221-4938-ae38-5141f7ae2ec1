# ========================================
# LOCAL NETWORK DEPLOYMENT CONFIGURATION
# ========================================
# Set deployment mode to 'local' for local network operation
deployment_mode: "local"

# Docker proxy - COMMENT OUT for local network deployment
# docker_proxy: "socks5://proxy.huecker.io:443"

# HTTPS/TLS Configuration - DISABLED for local network
# acme_email: "<EMAIL>"
# old_domain: "old.example.com" # optional
# domain: "example.com"

# Local network configuration - REPLACE with your server's IP
local_server_ip: "***********"  # CHANGE THIS to your server's actual IP address

# ========================================
# STORAGE CONFIGURATION (MinIO)
# ========================================
minio_user: "admin"  # Changed from AWS-style to simple admin
minio_password: "password123"  # Use a strong password for production
# ========================================
# ADMIN ACCOUNT CONFIGURATION
# ========================================
initial_admin_password: "admin"  # CHANGE THIS for production
initial_admin_password_salt: "salt012345678901234567" # should be 22-character

# ========================================
# EMAIL CONFIGURATION - DISABLED for local network
# ========================================
# Comment out email settings for local network deployment
# mail_host: "smtp.example.com"
# mail_port: "465"
# mail_from: "<EMAIL>"
# mail_username: "email_user"
# mail_password: "email_password"
# ========================================
# OAUTH 2.0 PROVIDERS - DISABLED for local network
# ========================================
# Comment out OAuth providers for local network deployment
# vkontakte_client_id: "6805077"
# vkontakte_client_secret: "your-app-client-secret"
# google_client_id: "*********-d12sja.apps.googleusercontent.com"
# google_client_secret: "your-app-client-secret"
# facebook_client_id: "***************"
# facebook_client_secret: "your-app-client-secret"
# ========================================
# APPLICATION SETTINGS
# ========================================
aaa_request_dump: "false"
chat_only_role_admin_can_create_blog: "false"
storage_limits_enabled: "false"
storage_all_user_limit: "536870912" # 512Mb
video_only_role_admin_can_record: "false"

# GitHub Actions SSH key - COMMENT OUT for local network
# github_actions_ssh_public_key: "ssh-rsa AAABBBCCC..."
# ========================================
# LIVEKIT CONFIGURATION FOR LOCAL NETWORK
# ========================================
livekit_max_participants_per_room: "50"  # Reduced for local network
livekit_api_key: "devkey"  # Simple key for local network
livekit_api_secret: "secret"  # Simple secret for local network

# Local network specific LiveKit settings - ADD THESE
livekit_use_external_ip: false
livekit_ice_lite: false
livekit_turn_enabled: false
livekit_development_mode: true
livekit_bind_all_interfaces: true
livekit_disable_external_stun: true
# ========================================
# MONITORING AND LOGGING CONFIGURATION
# ========================================
opensearch_scheduler_task_interval_minutes: "5"
opensearch_log_retention: "7d"  # Reduced for local network
opensearch_span_retention: "3d"  # Reduced for local network
opensearch_service_retention: "7d"  # Reduced for local network
aaa_online_estimation: "10m"

# ========================================
# LOCAL NETWORK SPECIFIC SETTINGS - ADD THESE
# ========================================
# Network configuration
local_network_mode: true
bind_all_interfaces: true
use_http_only: true
disable_external_dependencies: true

# Service binding configuration
service_bind_address: "0.0.0.0"

# Firewall ports for local network access
local_network_ports:
  - "8081/tcp"    # Main application
  - "8080/tcp"    # Traefik dashboard
  - "7880/tcp"    # LiveKit HTTP
  - "7881/tcp"    # LiveKit TCP RTC
  - "7882/udp"    # LiveKit UDP RTC (CRITICAL for video calls)
  - "39000/tcp"   # MinIO API
  - "39001/tcp"   # MinIO Console
  - "35672/tcp"   # RabbitMQ Management
  - "36672/tcp"   # RabbitMQ AMQP
  - "36379/tcp"   # Redis
  - "35432/tcp"   # PostgreSQL
  - "36686/tcp"   # Jaeger UI
  - "34318/tcp"   # Jaeger OTLP HTTP
  - "34317/tcp"   # Jaeger OTLP GRPC
  - "38080/tcp"   # PgAdmin
  - "9200/tcp"    # OpenSearch
  - "5601/tcp"    # OpenSearch Dashboards

# Database configuration for local network
postgresql_password: "postgresqlPassword"
chat_db_password: "chatPazZw0rd"
redis_password: ""  # No password for local network

# RabbitMQ configuration
rabbitmq_user: "videoChat"
rabbitmq_password: "videoChatPazZw0rd"

# SSL/TLS configuration - DISABLED for local network
ssl_enabled: false
letsencrypt_enabled: false
use_https: false

# External integrations - DISABLED for local network
ldap_enabled: false
external_auth_enabled: false
oauth2_enabled: false
email_notifications_enabled: false

# Development and debugging settings for local network
debug_mode: true
log_level: "DEBUG"
enable_tracing: true
enable_metrics: true
