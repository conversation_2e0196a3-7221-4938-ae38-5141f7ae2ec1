# Traefik configuration for local network deployment
# Optimized for HTTP-only operation without external dependencies

global:
  checkNewVersion: false
  sendAnonymousUsage: false

# Entry points for local network access
entryPoints:
  web:
    address: ":8081"
    # No HTTPS redirect for local network
  traefik:
    address: ":8080"

# API and dashboard configuration
api:
  dashboard: true
  insecure: true  # Allow insecure access for local network

# Providers configuration
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "{{ swarm_stack_name | lower }}_backend"
    watch: true

# Logging configuration
log:
  level: INFO
  format: common

accessLog:
  format: common

# Metrics for monitoring (optional)
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true
    addRoutersLabels: true

# Tracing configuration for local development
tracing:
  jaeger:
    samplingServerURL: http://jaeger:5778/sampling
    localAgentHostPort: jaeger:6831

# Ping endpoint for health checks
ping:
  entryPoint: "traefik"

# Certificate resolvers - disabled for local network
# certificatesResolvers: {}

# Disable automatic HTTPS for local network operation
serversTransport:
  insecureSkipVerify: true
