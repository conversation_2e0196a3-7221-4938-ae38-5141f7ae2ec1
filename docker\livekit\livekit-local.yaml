port: 7880
rtc:
    udp_port: 7882
    tcp_port: 7881
    use_external_ip: false
    # For local network operation - disable ICE lite for better compatibility
    ice_lite: false
    # Use local network interfaces
    node_ip: ""

# Use internal Redis
redis:
    address: redis:6379
    username: ""
    password: ""
    db: 2

# API keys (same as original)
keys:
    APIznJxWShGW3Kt: KEUUtCDVRqXk9me0Ok94g8G9xwtnjMeUxfNMy8dow6iA

logging:
  json: false
  level: info

# Internal webhook URL - use container networking
webhook:
  api_key: 'APIznJxWShGW3Kt'
  urls:
    - 'http://video:1237/internal/livekit-webhook'

audio:
  # minimum level to be considered active, 0-127, where 0 is loudest
  # defaults to 30
  active_level: 100

# Room configuration optimized for local network
room:
  enabled_codecs:
    - mime: audio/opus
    - mime: video/vp8
    - mime: video/h264
  auto_create: true
  # Disable simulcast for simpler local network operation
  enable_simulcast: false

# Development mode settings for local network
development: true

# Disable external TURN/STUN servers for offline operation
turn:
  enabled: false

# WebRTC configuration for local network
webrtc:
  # Use only local candidates
  ice_servers: []
