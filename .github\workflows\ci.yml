name: CI jobs

# https://github.com/actions/setup-go
# https://github.com/actions/setup-java
# https://github.com/actions/setup-node
# https://github.com/microsoft/playwright/issues/7249

on:
  push:

jobs:
  aaa:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up JDK 21
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-java@v4
        with:
          distribution: liberica
          java-version: 21.0.2
          cache: 'maven'
          cache-dependency-path: 'aaa/pom.xml'
      - name: Start & wait docker-compose environment
        if: env.SHOULD_RUN == 'true'
        run: |
          (
            cd aaa; 
            make infra infra-test
          )
      - name: Build and test with <PERSON><PERSON>
        if: env.SHOULD_RUN == 'true'
        run: |
          (cd aaa; make check-env clean download generate package test)
      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd aaa; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd aaa; make push-docker-image-to-registry)

  frontend:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up Node.js
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: 22.12.0
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'
      - name: Build with npm run
        if: env.SHOULD_RUN == 'true'
        run: |
          (cd frontend; make check-env clean download package)
      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd frontend; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd frontend; make push-docker-image-to-registry)

  public:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up Node.js
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: 22.12.0
          cache: 'npm'
          cache-dependency-path: 'public/package-lock.json'
      - name: Build with npm run
        if: env.SHOULD_RUN == 'true'
        run: |
          (cd public; make check-env clean download package)
      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd public; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd public; make push-docker-image-to-registry)

  chat:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up Go
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.4
          cache-dependency-path: 'chat/go.sum'
      - name: Start & wait docker-compose environment
        if: env.SHOULD_RUN == 'true'
        run: |
          (
            cd chat; 
            make infra infra-test
          )
      - name: Build and test with make
        if: env.SHOULD_RUN == 'true'
        run: (cd chat; make check-env clean download generate test package)

      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd chat; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd chat; make push-docker-image-to-registry)

  event:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up Go
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.4
          cache-dependency-path: 'event/go.sum'
      - name: Build and test with make
        if: env.SHOULD_RUN == 'true'
        run: (cd event; make check-env clean download generate test package)
      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd event; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd event; make push-docker-image-to-registry)

  notification:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up Go
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.4
          cache-dependency-path: 'notification/go.sum'
      - name: Build and test with make
        if: env.SHOULD_RUN == 'true'
        run: (cd notification; make check-env clean download generate test package)
      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd notification; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd notification; make push-docker-image-to-registry)

  storage:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up Go
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.4
          cache-dependency-path: 'storage/go.sum'
      - name: Build and test with make
        if: env.SHOULD_RUN == 'true'
        run: (cd storage; make check-env clean download generate test package)
      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd storage; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd storage; make push-docker-image-to-registry)

  video:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD ${GITHUB_JOB} ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"
      - name: Set up Go
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.4
          cache-dependency-path: 'video/go.sum'
      - name: Start & wait docker-compose environment
        if: env.SHOULD_RUN == 'true'
        run: |
          (
            cd video;
            make infra infra-test
          )
      - name: Build and test with make
        if: env.SHOULD_RUN == 'true'
        run: (cd video; make check-env clean download generate test package)
      - name: Deploy docker image to server
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          (
            mkdir -p ~/.ssh && openssl aes-256-cbc -K ${{ secrets.EncryptedKey }} -iv ${{ secrets.EncryptedIv }} -in ./.secret_keys/id_rsa.enc -out ~/.ssh/id_rsa -d
            chmod 600 ~/.ssh/id_rsa
            cd video; 
            export CONNECT_LINE=${{secrets.ConnectLine}}
            make push-docker-image-to-server deploy-docker-image
          )
      - name: Push docker image to registry
        if: env.SHOULD_RUN == 'true' && github.ref == 'refs/heads/master'
        run: |
          echo "Will login to docker" &&
          docker login -u="nkonev" -p="${{ secrets.DockerPassword }}" 
          (cd video; make push-docker-image-to-registry)

  e2e-test:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Decide should we build it
        run: ./scripts/should-run.sh HEAD 'frontend|aaa|chat|event|e2e-test' ${{ secrets.WebSitePrefix }} && echo "SHOULD_RUN=true" >> "$GITHUB_ENV" || echo "SHOULD_RUN=false" >> "$GITHUB_ENV"

      - name: Set up JDK 21
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-java@v4
        with:
          distribution: liberica
          java-version: 21.0.2
          cache: 'maven'
          cache-dependency-path: 'aaa/pom.xml'

      - uses: actions/setup-node@v4
        if: env.SHOULD_RUN == 'true'
        with:
          node-version: 22.12.0
          cache: 'npm'
          cache-dependency-path: |
            frontend/package-lock.json
            e2e-test/package-lock.json
      - name: Cache Playwright browsers and Node.js dependencies
        if: env.SHOULD_RUN == 'true'
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-browsers-v1-${{ hashFiles('**/package-lock.json') }}

      - name: Install Playwright Node.js dependencies and browsers
        if: env.SHOULD_RUN == 'true'
        run: (cd e2e-test; make check-env clean download)
      - name: Set up Go
        if: env.SHOULD_RUN == 'true'
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.4
          cache-dependency-path: |
            chat/go.sum
            event/go.sum

      - name: Start and wait docker-compose
        if: env.SHOULD_RUN == 'true'
        run: |
          make infra
      - name: Build microservices
        if: env.SHOULD_RUN == 'true'
        run: |
          (cd aaa; make check-env clean download generate package-java)
          (cd frontend; make check-env clean download package)
          (cd chat; make check-env clean download generate package-go)
          (cd event; make check-env clean download generate package-go)
          (cd notification; make check-env clean download generate package-go)
          (cd video; make check-env clean download generate package-go)
      - name: Run microservices
        if: env.SHOULD_RUN == 'true'
        run: |
          (cd aaa; make run-with-oauth2 &)
          (cd chat; make run &)
          (cd event; make run &)
          (cd notification; make run &)
          (cd video; make run &)
          (cd frontend; make run-docker-image &)
      - name: Wait for microservices
        if: env.SHOULD_RUN == 'true'
        run: |
          ./scripts/wait-for-it.sh -t 40 127.0.0.1:8060 -- echo 'aaa is up' # because aaa creates schema
          ./scripts/wait-for-it.sh -t 40 127.0.0.1:1235 -- echo 'chat is up'
          ./scripts/wait-for-it.sh -t 40 127.0.0.1:1238 -- echo 'event is up'
          ./scripts/wait-for-it.sh -t 40 127.0.0.1:1230 -- echo 'notification is up'
          ./scripts/wait-for-it.sh -t 40 127.0.0.1:1237 -- echo 'video is up'
          ./scripts/wait-for-it.sh -t 40 127.0.0.1:3000 -- echo 'front is up'
          # Once we started aaa (compiling) we start the emulator
          (cd aaa; make run-oauth2-emu &)
          ./scripts/wait-for-it.sh -t 40 127.0.0.1:9080 -- echo 'oauth2 emulator controller is up'
      - name: Run your Playwright tests
        if: env.SHOULD_RUN == 'true'
        run: (cd e2e-test; make test)
      - name: Upload test results
        if: env.SHOULD_RUN == 'true' && always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-results
          path: e2e-test/test-results
