package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.74

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/montag451/go-eventbus"
	"go.opentelemetry.io/otel/attribute"
	"nkonev.name/event/auth"
	"nkonev.name/event/dto"
	"nkonev.name/event/graph/model"
	"nkonev.name/event/rabbitmq"
	"nkonev.name/event/utils"
)

// Ping is the resolver for the ping field.
func (r *queryResolver) Ping(ctx context.Context) (*bool, error) {
	res := true
	return &res, nil
}

// ChatEvents is the resolver for the chatEvents field.
func (r *subscriptionResolver) ChatEvents(ctx context.Context, chatID int64) (<-chan *model.ChatEvent, error) {
	authResult, ok := ctx.Value(utils.USER_PRINCIPAL_DTO).(*auth.AuthResult)
	if !ok {
		return nil, errors.New("Unable to get auth context")
	}

	hasAccess, err := r.HttpClient.CheckAccess(ctx, authResult.UserId, chatID)
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during checking participant user %v, chat %v", authResult.UserId, chatID)
		return nil, err
	}
	if !hasAccess {
		r.Lgr.WithTracing(ctx).Infof("User %v is not participant of chat %v", authResult.UserId, chatID)
		return nil, errors.New("Unauthorized")
	}
	r.Lgr.WithTracing(ctx).Infof("Subscribing to chatEvents channel as user %v", authResult.UserId)

	var cam = make(chan *model.ChatEvent)
	subscribeHandler, err := r.Bus.Subscribe(dto.CHAT_EVENTS, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing ChatEvents panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.ChatEvent:
			if isReceiverOfEvent(typedEvent.UserId, authResult) && typedEvent.ChatId == chatID {
				_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", typedEvent.EventType))
				defer span.End()
				span.SetAttributes(
					attribute.Int64("userId", typedEvent.UserId),
					attribute.Int64("chatId", typedEvent.ChatId),
				)

				cam <- convertToChatEvent(&typedEvent)
			}
			break
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v, chat %v", typedEvent, authResult.UserId, chatID)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v, chat %v", authResult.UserId, chatID)
		return nil, err
	}

	go func() {
		for {
			select {
			case <-ctx.Done():
				r.Lgr.WithTracing(ctx).Infof("Closing chatEvents channel for user %v", authResult.UserId)
				err := r.Bus.Unsubscribe(subscribeHandler)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in chatEvents channel for user %v", authResult.UserId)
				}
				close(cam)
				return
			}
		}
	}()

	return cam, nil
}

// GlobalEvents is the resolver for the globalEvents field.
func (r *subscriptionResolver) GlobalEvents(ctx context.Context) (<-chan *model.GlobalEvent, error) {
	authResult, ok := ctx.Value(utils.USER_PRINCIPAL_DTO).(*auth.AuthResult)
	if !ok {
		return nil, errors.New("Unable to get auth context")
	}
	r.Lgr.WithTracing(ctx).Infof("Subscribing to globalEvents channel as user %v", authResult.UserId)

	var cam = make(chan *model.GlobalEvent)
	globalSubscribeHandler, err := r.Bus.Subscribe(dto.GLOBAL_USER_EVENTS, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing GlobalEvents panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.GlobalUserEvent:
			if isReceiverOfEvent(typedEvent.UserId, authResult) {
				_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", typedEvent.EventType))
				defer span.End()
				span.SetAttributes(
					attribute.Int64("userId", typedEvent.UserId),
				)

				cam <- convertToGlobalEvent(&typedEvent)
			}
			break
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v", typedEvent, authResult.UserId)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v", authResult.UserId)
		return nil, err
	}
	killSessionsSubscribeHandler, err := r.Bus.Subscribe(dto.AAA_KILL_SESSIONS, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing GlobalEvents panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.UserSessionsKilledEvent:
			if isReceiverOfEvent(typedEvent.UserId, authResult) {
				_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", typedEvent.EventType))
				defer span.End()
				span.SetAttributes(
					attribute.Int64("userId", typedEvent.UserId),
				)

				cam <- convertToUserSessionsKilledEvent(&typedEvent)
			}
			break
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v", typedEvent, authResult.UserId)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v", authResult.UserId)
		return nil, err
	}

	go func() {
		for {
			select {
			case <-ctx.Done():

				r.Lgr.WithTracing(ctx).Infof("Closing globalEvents channel for user %v", authResult.UserId)
				err := r.Bus.Unsubscribe(globalSubscribeHandler)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in globalEvents channel for user %v", authResult.UserId)
				}

				r.Lgr.WithTracing(ctx).Infof("Closing killSessionsSubscribeHandler channel for user %v", authResult.UserId)
				err = r.Bus.Unsubscribe(killSessionsSubscribeHandler)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in UserVideoStatus channel for user %v", authResult.UserId)
				}

				close(cam)
				return
			}
		}
	}()

	return cam, nil
}

// UserStatusEvents is the resolver for the userStatusEvents field.
func (r *subscriptionResolver) UserStatusEvents(ctx context.Context, userIds []int64) (<-chan []*model.UserStatusEvent, error) {
	// user online
	authResult, ok := ctx.Value(utils.USER_PRINCIPAL_DTO).(*auth.AuthResult)
	if !ok {
		return nil, errors.New("Unable to get auth context")
	}
	r.Lgr.WithTracing(ctx).Infof("Subscribing to UserOnline channel as user %v", authResult.UserId)

	var cam = make(chan []*model.UserStatusEvent)

	subscribeHandlerUserOnline, err := r.Bus.Subscribe(dto.USER_ONLINE, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing UserOnline panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.ArrayUserOnline:
			var batch = []*model.UserStatusEvent{}
			for _, userOnline := range typedEvent.UserOnlines {
				if utils.Contains(userIds, userOnline.UserId) {
					_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", "user_online"))
					defer span.End()
					span.SetAttributes(
						attribute.Int64("userId", userOnline.UserId),
					)

					batch = append(batch, convertToUserOnline(userOnline))
				}
			}
			if len(batch) > 0 {
				cam <- batch
			}
			break
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v", typedEvent, authResult.UserId)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v", authResult.UserId)
		return nil, err
	}

	subscribeHandlerVideoCallStatus, err := r.Bus.Subscribe(dto.GENERAL, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing UserVideoStatus panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.GeneralEvent:
			var videoCallUsersCallStatusChangedEvent = typedEvent.VideoCallUsersCallStatusChangedEvent
			if videoCallUsersCallStatusChangedEvent != nil {
				var batch = []*model.UserStatusEvent{}
				for _, userCallStatus := range videoCallUsersCallStatusChangedEvent.Users {
					if utils.Contains(userIds, userCallStatus.UserId) {
						_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", typedEvent.EventType))
						defer span.End()
						span.SetAttributes(
							attribute.Int64("userId", userCallStatus.UserId),
						)

						batch = append(batch, convertToUserCallStatusChanged(typedEvent, userCallStatus))
					}
				}
				if len(batch) > 0 {
					cam <- batch
				}
			}
			break
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v", typedEvent, authResult.UserId)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v", authResult.UserId)
		return nil, err
	}

	go func() {
		for {
			select {
			case <-ctx.Done():
				r.Lgr.WithTracing(ctx).Infof("Closing UserOnline channel for user %v", authResult.UserId)
				err := r.Bus.Unsubscribe(subscribeHandlerUserOnline)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in UserOnline channel for user %v", authResult.UserId)
				}

				r.Lgr.WithTracing(ctx).Infof("Closing UserVideoStatus channel for user %v", authResult.UserId)
				err = r.Bus.Unsubscribe(subscribeHandlerVideoCallStatus)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in UserVideoStatus channel for user %v", authResult.UserId)
				}

				close(cam)
				return
			}
		}
	}()

	return cam, nil
}

// UserAccountEvents is the resolver for the userAccountEvents field.
func (r *subscriptionResolver) UserAccountEvents(ctx context.Context, userIdsFilter []int64) (<-chan *model.UserAccountEvent, error) {
	authResult, ok := ctx.Value(utils.USER_PRINCIPAL_DTO).(*auth.AuthResult)
	if !ok {
		return nil, errors.New("Unable to get auth context")
	}
	r.Lgr.WithTracing(ctx).Infof("Subscribing to UserAccount channel as user %v", authResult.UserId)

	var cam = make(chan *model.UserAccountEvent)

	subscribeHandlerAaaChange, err := r.Bus.Subscribe(dto.AAA_CHANGE, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing UserAccount panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.UserAccountEventChanged:
			if filter(typedEvent.UserId, userIdsFilter) {
				var anEvent = r.prepareUserAccountEvent(ctx, authResult.UserId, typedEvent.EventType, typedEvent.User)
				if anEvent != nil {
					_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", typedEvent.EventType))
					defer span.End()
					span.SetAttributes(
						attribute.Int64("userId", typedEvent.UserId),
					)

					cam <- anEvent
				}
			}
			break
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v", typedEvent, authResult.UserId)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v", authResult.UserId)
		return nil, err
	}

	subscribeHandlerAaaCreate, err := r.Bus.Subscribe(dto.AAA_CREATE, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing UserAccount panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.UserAccountEventCreated:
			if filter(typedEvent.UserId, userIdsFilter) {
				var anEvent = r.prepareUserAccountEvent(ctx, authResult.UserId, typedEvent.EventType, typedEvent.User)
				if anEvent != nil {
					_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", typedEvent.EventType))
					defer span.End()
					span.SetAttributes(
						attribute.Int64("userId", typedEvent.UserId),
					)

					cam <- anEvent
				}
			}
			break
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v", typedEvent, authResult.UserId)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v", authResult.UserId)
		return nil, err
	}

	subscribeHandlerAaaDelete, err := r.Bus.Subscribe(dto.AAA_DELETE, func(event eventbus.Event, t time.Time) {
		defer func() {
			if err := recover(); err != nil {
				r.Lgr.WithTracing(ctx).Errorf("In processing UserAccount panic recovered: %v", err)
			}
		}()

		switch typedEvent := event.(type) {
		case dto.UserAccountEventDeleted:
			if filter(typedEvent.UserId, userIdsFilter) {
				var anEvent = convertUserAccountDeletedEvent(typedEvent.EventType, typedEvent.UserId)
				if anEvent != nil {
					_, span := r.Tr.Start(rabbitmq.DeserializeValues(ctx, r.Lgr, typedEvent.TraceString), fmt.Sprintf("subscription.%s", typedEvent.EventType))
					defer span.End()
					span.SetAttributes(
						attribute.Int64("userId", typedEvent.UserId),
					)

					cam <- anEvent
				}
			}
		default:
			r.Lgr.WithTracing(ctx).Debugf("Skipping %v as is no mapping here for this type, user %v", typedEvent, authResult.UserId)
		}
	})
	if err != nil {
		r.Lgr.WithTracing(ctx).Errorf("Error during creating eventbus subscription user %v", authResult.UserId)
		return nil, err
	}

	go func() {
		for {
			select {
			case <-ctx.Done():
				r.Lgr.WithTracing(ctx).Infof("Closing UserAccount change channel for user %v", authResult.UserId)
				err := r.Bus.Unsubscribe(subscribeHandlerAaaChange)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in UserAccount change channel for user %v", authResult.UserId)
				}

				r.Lgr.WithTracing(ctx).Infof("Closing UserAccount create channel for user %v", authResult.UserId)
				err = r.Bus.Unsubscribe(subscribeHandlerAaaCreate)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in UserAccount create channel for user %v", authResult.UserId)
				}

				r.Lgr.WithTracing(ctx).Infof("Closing UserAccount delete channel for user %v", authResult.UserId)
				err = r.Bus.Unsubscribe(subscribeHandlerAaaDelete)
				if err != nil {
					r.Lgr.WithTracing(ctx).Errorf("Error during unsubscribing from bus in UserAccount delete channel for user %v", authResult.UserId)
				}

				close(cam)
				return
			}
		}
	}()

	return cam, nil
}

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

// Subscription returns SubscriptionResolver implementation.
func (r *Resolver) Subscription() SubscriptionResolver { return &subscriptionResolver{r} }

type queryResolver struct{ *Resolver }
type subscriptionResolver struct{ *Resolver }
