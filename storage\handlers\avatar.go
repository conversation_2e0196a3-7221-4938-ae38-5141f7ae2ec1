package handlers

import (
	"bytes"
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/minio/minio-go/v7"
	"github.com/siyouyun-open/imaging"
	"image"
	"image/jpeg"
	"net/http"
	"nkonev.name/storage/auth"
	"nkonev.name/storage/logger"
	"nkonev.name/storage/s3"
	"nkonev.name/storage/utils"
	"strconv"
	"time"
)

const FormFile = "data"

// Go enum
type AvatarType string

const (
	AVATAR_200x200 AvatarType = "AVATAR_200x200"
	AVATAR_640x640 AvatarType = "AVATAR_640x640"
)

type abstractMethods interface {
	getAvatarFileName(c echo.Context, avatarType AvatarType) (string, error)
	ensureAndGetAvatarBucket() (string, error)
	GetUrlPath() string
}

type abstractAvatarHandler struct {
	minio       *s3.InternalMinioClient
	minioConfig *utils.MinioConfig
	delegate    abstractMethods
	lgr         *logger.Logger
}

func (h *abstractAvatarHandler) PutAvatar(c echo.Context) error {
	filePart, err := c.FormFile(FormFile)
	if err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during extracting form %v parameter: %v", FormFile, err)
		return err
	}

	bucketName, err := h.delegate.ensureAndGetAvatarBucket()
	if err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during get bucket: %v", err)
		return err
	}

	contentType := filePart.Header.Get("Content-Type")

	h.lgr.WithTracing(c.Request().Context()).Debugf("Determined content type: %v", contentType)

	src, err := filePart.Open()
	if err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during opening multipart file: %v", err)
		return err
	}
	defer src.Close()

	srcImage, _, err := image.Decode(src)
	if err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during decoding image: %v", err)
		return err
	}

	currTime := time.Now().UTC().Unix()
	filename200, relativeUrl, err := h.putSizedFile(c, srcImage, err, bucketName, contentType, 200, 200, AVATAR_200x200, currTime)
	if err != nil {
		return err
	}
	filename640, relativeBigUrl, err := h.putSizedFile(c, srcImage, err, bucketName, contentType, 640, 640, AVATAR_640x640, currTime)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, &utils.H{"status": "ok", "filename": filename200, "filenameBig": filename640, "relativeUrl": relativeUrl, "relativeBigUrl": relativeBigUrl})
}

func (h *abstractAvatarHandler) putSizedFile(c echo.Context, srcImage image.Image, err error, bucketName string, contentType string, width, height int, avatarType AvatarType, currTime int64) (string, string, error) {
	dstImage := imaging.Resize(srcImage, width, height, imaging.Lanczos)
	byteBuffer := new(bytes.Buffer)
	err = jpeg.Encode(byteBuffer, dstImage, nil)
	if err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during encoding image: %v", err)
		return "", "", err
	}
	filename, err := h.getAvatarFileName(c, avatarType)
	if err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during get avatar filename: %v", err)
		return "", "", err
	}
	if _, err := h.minio.PutObject(c.Request().Context(), bucketName, filename, byteBuffer, int64(byteBuffer.Len()), minio.PutObjectOptions{ContentType: contentType}); err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during upload object: %v", err)
		return "", "", err
	}
	relativeUrl := fmt.Sprintf("%v/%v?%v=%v", h.delegate.GetUrlPath(), filename, utils.TimeParam, currTime)

	return filename, relativeUrl, nil
}

func (r *abstractAvatarHandler) getAvatarFileName(c echo.Context, avatarType AvatarType) (string, error) {
	return r.delegate.getAvatarFileName(c, avatarType)
}

func (h *abstractAvatarHandler) Download(c echo.Context) error {
	bucketName, err := h.delegate.ensureAndGetAvatarBucket()
	if err != nil {
		h.lgr.WithTracing(c.Request().Context()).Errorf("Error during get bucket: %v", err)
		return err
	}

	objId := c.Param("filename")

	exists, info, err := h.minio.FileExists(c.Request().Context(), bucketName, objId)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, &utils.H{"status": "stat fail"})
	}
	if !exists {
		return c.JSON(http.StatusNotFound, &utils.H{"status": "not found"})
	}

	c.Response().Header().Set(echo.HeaderContentLength, strconv.FormatInt(info.Size, 10))
	c.Response().Header().Set(echo.HeaderContentType, info.ContentType)
	//c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; Filename=\""+mongoDto.Filename+"\"")

	object, e := h.minio.GetObject(c.Request().Context(), bucketName, objId, minio.GetObjectOptions{})
	if e != nil {
		return c.JSON(http.StatusInternalServerError, &utils.H{"status": "fail"})
	}
	defer object.Close()

	avatarCacheableResponse(c)
	return c.Stream(http.StatusOK, info.ContentType, object)
}

type UserAvatarHandler struct {
	abstractAvatarHandler
}

func NewUserAvatarHandler(lgr *logger.Logger, minio *s3.InternalMinioClient, minioConfig *utils.MinioConfig) *UserAvatarHandler {
	uah := UserAvatarHandler{}
	uah.minio = minio
	uah.delegate = &uah
	uah.minioConfig = minioConfig
	uah.lgr = lgr
	return &uah
}

const urlStorageGetUserAvatar = "/api/storage/public/user/avatar"

func (h *UserAvatarHandler) ensureAndGetAvatarBucket() (string, error) {
	return h.minioConfig.UserAvatar, nil
}

func (r *UserAvatarHandler) getAvatarFileName(c echo.Context, avatarType AvatarType) (string, error) {
	var userPrincipalDto, ok = c.Get(utils.USER_PRINCIPAL_DTO).(*auth.AuthResult)
	if !ok {
		r.lgr.WithTracing(c.Request().Context()).Errorf("Error during getting auth context")
		return "", errors.New("Error during getting auth context")
	}
	return fmt.Sprintf("%v_%v.jpg", userPrincipalDto.UserId, avatarType), nil
}

func (r *UserAvatarHandler) GetUrlPath() string {
	return urlStorageGetUserAvatar
}

type ChatAvatarHandler struct {
	abstractAvatarHandler
}

func NewChatAvatarHandler(lgr *logger.Logger, minio *s3.InternalMinioClient, minioConfig *utils.MinioConfig) *ChatAvatarHandler {
	uah := ChatAvatarHandler{}
	uah.minio = minio
	uah.delegate = &uah
	uah.minioConfig = minioConfig
	uah.lgr = lgr
	return &uah
}

const urlStorageGetChatAvatar = "/api/storage/public/chat/avatar"

func (h *ChatAvatarHandler) ensureAndGetAvatarBucket() (string, error) {
	return h.minioConfig.ChatAvatar, nil
}

func (r *ChatAvatarHandler) getAvatarFileName(c echo.Context, avatarType AvatarType) (string, error) {
	return fmt.Sprintf("%v_%v.jpg", c.Param("chatId"), avatarType), nil
}

func (r *ChatAvatarHandler) GetUrlPath() string {
	return urlStorageGetChatAvatar
}
