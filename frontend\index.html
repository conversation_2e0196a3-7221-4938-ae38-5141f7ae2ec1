<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.svg" />
  <!-- https://developer.chrome.com/blog/viewport-resize-behavior/ -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, interactive-widget=resizes-content">
  <title>Welcome to VideoChat</title>
</head>

<body>
  <div id="app">VideoChat app will be loaded soon. Press <button onclick="location.reload()" style=" background-color: revert; border: revert; outline: revert;">Refresh</button> if not.</div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>
