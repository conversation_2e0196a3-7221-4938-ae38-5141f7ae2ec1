server:
  address: ":1230"
  shutdown.timeout: "10s"
  body.limit: "100G"

logger:
  level: info
  dir: "log"
  filename: "file.log"
  writeToFile: true

# Rest client
http:
  maxIdleConns: 2
  idleConnTimeout: '10s'
  disableCompression: false

auth:
  exclude:
    - "^/api/notification/public.*"
    - "^/internal.*"

postgresql:
  # https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING
  url: "postgres://notification:notificationPazZw0rd@localhost:35432/notification?sslmode=disable&application_name=notification-app"
  maxOpenConnections: 16
  maxIdleConnections: 4
  maxLifetime: 30s

rabbitmq:
  url: "amqp://videoChat:videoChatPazZw0rd@127.0.0.1:36672"

otlp:
  endpoint: "localhost:34317"

maxNotificationsPerUser: 1024
