# docker_proxy is optional
docker_proxy: "socks5://proxy.huecker.io:443"
acme_email: "<EMAIL>"
old_domain: "old.example.com" # optional
domain: "example.com"
minio_user: "AKIAIOSFODNN7EXAMPLE"
minio_password: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
initial_admin_password: "admin"
initial_admin_password_salt: "salt012345678901234567" # should be 22-character, https://docs.ansible.com/ansible/latest/collections/ansible/builtin/password_hash_filter.html
mail_host: "smtp.example.com"
mail_port: "465"
mail_from: "<EMAIL>"
mail_username: "email_user"
mail_password: "email_password"
# OAuth 2.0 providers, all are optional
vkontakte_client_id: "6805077"
vkontakte_client_secret: "your-app-client-secret"
google_client_id: "987654321-d12sja.apps.googleusercontent.com"
google_client_secret: "your-app-client-secret"
facebook_client_id: "333333333333333"
facebook_client_secret: "your-app-client-secret"
aaa_request_dump: "false"
chat_only_role_admin_can_create_blog: "false"
storage_limits_enabled: "false"
storage_all_user_limit: "536870912" # 512Mb
video_only_role_admin_can_record: "false"
github_actions_ssh_public_key: "ssh-rsa AAABBBCCC..."
livekit_max_participants_per_room: "100"
livekit_api_key: "APIznJxWShGW3Kt"
livekit_api_secret: "KEUUtCDVRqXk9me0Ok94g8G9xwtnjMeUxfNMy8dow6iA"
opensearch_scheduler_task_interval_minutes: "5"
opensearch_log_retention: "14d"
opensearch_span_retention: "7d"
opensearch_service_retention: "14d"
aaa_online_estimation: "10m"
