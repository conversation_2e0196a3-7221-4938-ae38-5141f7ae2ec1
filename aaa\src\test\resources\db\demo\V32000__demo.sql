-- insert test data
INSERT INTO user_account(login, password, avatar, email, confirmed) VALUES
	('nikita', '$2a$10$e3pEnL2d3RB7jBrlEA3B9eUhayb/bmEG1V35h.4EhdReUAMzlAWxS', '/654853-user-men-2-512.png', '<EMAIL>', true), -- bcrypt('password', 10)
	('alice', '$2a$10$e3pEnL2d3RB7jBrlEA3B9eUhayb/bmEG1V35h.4EhdReUAMzlAWxS', '/girl-512.png', '<EMAIL>', true),
	('bob', '$2a$10$e3pEnL2d3RB7jBrlEA3B9eUhayb/bmEG1V35h.4EhdReUAMzlAWxS', NULL, '<EMAIL>', true),
	('<PERSON>', '$2a$10$e3pEnL2d3RB7jBrlEA3B9eUhayb/bmEG1V35h.4EhdReUAMzlAWxS', NULL, '<EMAIL>', true)
;
-- insert many test users
INSERT INTO user_account (login, password, avatar, email, confirmed)
	SELECT
    'generated_user_' || i,
    '$2a$10$0nGRZ4Quy0hW2W.prjc.AOyUkNqgFulVckZQ.gFsOly5ESntrW7E.', -- bcrypt('generated_user_password', 10)
    CASE
      WHEN i % 2 = 0 THEN '/Avatar_Alien-512.png'
      ELSE NULL
    END,
		'generated' || i || '@example.com',
         true
	FROM generate_series(0, 1000) AS i;


-- insert additional users and roles
INSERT INTO user_account(login, password, avatar, email, confirmed) VALUES
	('forgot-password-user', '$2a$10$e3pEnL2d3RB7jBrlEA3B9eUhayb/bmEG1V35h.4EhdReUAMzlAWxS', NULL, '<EMAIL>', true);

