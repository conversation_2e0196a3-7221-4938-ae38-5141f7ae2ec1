@echo off
setlocal enabledelayedexpansion

REM Videochat Local Network Setup Script for Windows
REM This script configures the videochat application for local network operation

echo 🚀 Setting up Videochat for Local Network Operation
echo ==================================================

REM Get the local IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set "ip=%%a"
    set "ip=!ip: =!"
    if not "!ip!"=="" (
        set LOCAL_IP=!ip!
        goto :found_ip
    )
)

:found_ip
if "%LOCAL_IP%"=="" (
    echo ❌ Could not detect local IP address
    pause
    exit /b 1
)

echo 📍 Detected local IP address: %LOCAL_IP%

REM Create environment file for local network
(
echo # Local Network Configuration
echo LOCAL_IP=%LOCAL_IP%
echo FRONTEND_URL=http://%LOCAL_IP%:8081
echo CHAT_API_URL=http://%LOCAL_IP%:1235
echo AAA_API_URL=http://%LOCAL_IP%:8060
echo.
echo # Disable external services
echo DISABLE_EXTERNAL_SERVICES=true
) > .env.local

echo ✅ Created .env.local with local network settings

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker Desktop and try again.
    pause
    exit /b 1
)

echo ✅ Docker is running

REM Check if docker compose is available
docker compose version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose is not available. Please install Docker Compose.
    pause
    exit /b 1
)

echo ✅ Docker Compose is available

REM Stop any existing containers
echo 🛑 Stopping existing containers...
docker compose -f docker-compose.yml -f docker-compose.local.yml down

REM Pull required images (if internet is available)
echo 📥 Pulling Docker images (if internet is available)...
ping -n 1 ******* >nul 2>&1
if errorlevel 1 (
    echo 🔒 No internet connection detected, using local images...
) else (
    echo 🌐 Internet connection detected, pulling latest images...
    docker compose -f docker-compose.yml -f docker-compose.local.yml pull
)

REM Start the infrastructure
echo 🏗️  Starting infrastructure services...
docker compose -f docker-compose.yml -f docker-compose.local.yml up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 15 /nobreak >nul

echo 🔍 Checking service health...

REM Check critical services using telnet-like functionality
echo ✅ Services should be starting up...

echo.
echo 🎉 Videochat Local Network Setup Complete!
echo ==========================================
echo.
echo 📱 Access the application:
echo    Main Application: http://%LOCAL_IP%:8081
echo    Traefik Dashboard: http://%LOCAL_IP%:8080
echo.
echo 🔧 Administrative Interfaces:
echo    MinIO Console: http://%LOCAL_IP%:39001
echo    RabbitMQ Management: http://%LOCAL_IP%:35672
echo    Jaeger Tracing: http://%LOCAL_IP%:36686
echo    OpenSearch Dashboards: http://%LOCAL_IP%:5601
echo.
echo 📋 Default Credentials:
echo    MinIO: AKIAIOSFODNN7EXAMPLE / wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
echo    RabbitMQ: videoChat / videoChatPazZw0rd
echo    PostgreSQL: postgres / postgresqlPassword
echo.
echo 🔗 Share this URL with other devices on your local network:
echo    http://%LOCAL_IP%:8081
echo.
echo 📝 To stop the application:
echo    docker compose -f docker-compose.yml -f docker-compose.local.yml down
echo.
echo 🔄 To view logs:
echo    docker compose -f docker-compose.yml -f docker-compose.local.yml logs -f
echo.
echo Press any key to continue...
pause >nul
