# Docker Swarm Local Network Fixes

## 🚨 **The Problem**

The error you encountered shows that the Ansible playbook uses **Docker Swarm** (`docker stack deploy`), but the local network templates I initially created were designed for **Docker Compose**:

```
failed to create service VIDEOCHATSTACK_egress: Error response from daemon: 
The network VIDEOCHATSTACK_backend cannot be used with services. 
Only networks scoped to the swarm can be used, such as those created with the overlay driver.
```

## 🔧 **Key Differences: Docker Compose vs Docker Swarm**

### **Docker Compose Format (WRONG for Ansible)**
```yaml
ports:
  - "0.0.0.0:8081:8081"  # Simple format
networks:
  backend:
    driver: bridge  # Bridge driver
```

### **Docker Swarm Format (CORRECT for Ansible)**
```yaml
ports:
  - target: 8081
    published: 8081
    protocol: tcp
    mode: host  # Host mode for external access
networks:
  backend:
    driver: overlay  # Overlay driver for swarm
```

## ✅ **Fixes Applied**

### **1. Network Configuration Fixed**
**Before (Docker Compose):**
```yaml
networks:
  backend:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.host_binding_ipv4: "0.0.0.0"
```

**After (Docker Swarm):**
```yaml
networks:
  backend:
    driver: overlay
    driver_opts:
      encrypted: "false"
    attachable: true
```

### **2. Port Configuration Fixed**
**Before (Docker Compose):**
```yaml
ports:
  - "0.0.0.0:7882:7882/udp"  # LiveKit UDP
```

**After (Docker Swarm):**
```yaml
ports:
  - target: 7882
    published: 7882
    protocol: udp
    mode: host  # Essential for local network access
```

### **3. Restart Policy Fixed**
**Before (Docker Compose):**
```yaml
restart: unless-stopped
```

**After (Docker Swarm):**
```yaml
deploy:
  restart_policy:
    condition: unless-stopped
```

### **4. Volume Paths Fixed**
**Before (Absolute paths):**
```yaml
volumes:
  - {{ dir_prefix }}/livekit/livekit.yaml:/etc/livekit.yaml
```

**After (Relative paths like original):**
```yaml
volumes:
  - ./livekit/livekit.yaml:/etc/livekit.yaml
```

## 🎯 **Critical Ports Fixed for Local Network**

All these ports now use Docker Swarm format with `mode: host` for local network access:

- **8081/tcp** - Main application (Traefik)
- **8080/tcp** - Traefik dashboard
- **7880/tcp** - LiveKit HTTP API
- **7881/tcp** - LiveKit TCP RTC
- **7882/udp** - LiveKit UDP RTC (CRITICAL for video calls)
- **39000/tcp** - MinIO API
- **39001/tcp** - MinIO Console
- **35672/tcp** - RabbitMQ Management
- **36672/tcp** - RabbitMQ AMQP
- **36379/tcp** - Redis
- **35432/tcp** - PostgreSQL
- **36686/tcp** - Jaeger UI
- **38080/tcp** - PgAdmin
- **9200/tcp** - OpenSearch
- **5601/tcp** - OpenSearch Dashboards

## 🚀 **How Ansible Deploys with Docker Swarm**

The Ansible playbook uses these tasks:

```yaml
- name: Init a new swarm with default parameters
  community.docker.docker_swarm:
    state: present

- name: Deploy stack for Infra
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-infra.yml"
```

This is equivalent to running:
```bash
docker swarm init
docker stack deploy -c docker-compose-infra.yml VIDEOCHATSTACK
```

## 🔍 **Why This Matters for Local Network**

### **1. Network Isolation**
- Docker Swarm creates **overlay networks** that span multiple nodes
- Even on single node, it provides better service discovery
- Services can communicate using service names (e.g., `http://livekit:7880`)

### **2. Port Publishing**
- `mode: host` publishes ports directly on the host interface
- Essential for local network access from other devices
- Bypasses Docker's internal networking for external access

### **3. Service Management**
- Docker Swarm provides better service lifecycle management
- Automatic restart policies
- Health checks and rolling updates

## 🎯 **What This Fixes**

1. **Network Creation Error**: Overlay driver now works with Docker Swarm
2. **Port Binding**: All services accessible from local network
3. **Service Communication**: Internal service-to-service communication works
4. **LiveKit WebRTC**: UDP port 7882 properly exposed for video calls
5. **Management Interfaces**: All admin interfaces accessible

## 🚨 **Important Notes**

### **For Local Network Deployment:**
- All services run on **single Docker Swarm node**
- **Overlay network** provides service isolation
- **Host mode ports** allow local network access
- **No external dependencies** (STUN/TURN disabled)

### **Key Differences from Cloud:**
- **HTTP only** (no HTTPS/TLS)
- **Direct port binding** (no reverse proxy complexity)
- **Simplified authentication** (no OAuth providers)
- **Local network optimized** (no external APIs)

## ✅ **Verification**

After deployment, verify with:

```bash
# Check Docker Swarm status
docker node ls

# Check services
docker service ls

# Check networks
docker network ls | grep overlay

# Test LiveKit (most critical)
curl http://YOUR_SERVER_IP:7880

# Test main application
curl http://YOUR_SERVER_IP:8081
```

The fixed templates now properly support Docker Swarm deployment while maintaining local network optimization for offline operation.
