#!/bin/bash

# Videochat Local Network Setup Script
# This script configures the videochat application for local network operation

set -e

echo "🚀 Setting up Videochat for Local Network Operation"
echo "=================================================="

# Function to get local IP address
get_local_ip() {
    # Try different methods to get local IP
    if command -v hostname >/dev/null 2>&1; then
        LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
    fi

    if [ -z "$LOCAL_IP" ]; then
        LOCAL_IP=$(ip route get 1 | awk '{print $7; exit}' 2>/dev/null)
    fi

    if [ -z "$LOCAL_IP" ]; then
        LOCAL_IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi

    if [ -z "$LOCAL_IP" ]; then
        echo "❌ Could not detect local IP address automatically"
        echo "Please enter your local IP address manually:"
        read -p "Local IP: " LOCAL_IP
    fi
}

get_local_ip
echo "📍 Using local IP address: $LOCAL_IP"

# Update .env.local with detected IP
sed -i "s/YOUR_LOCAL_IP/$LOCAL_IP/g" .env.local
echo "✅ Updated .env.local with local network settings"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "✅ Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    if ! docker compose version > /dev/null 2>&1; then
        echo "❌ Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

echo "✅ Docker Compose is available"

# Stop any existing containers
echo "🛑 Stopping existing containers..."
$DOCKER_COMPOSE -f docker-compose.yml -f docker-compose.local.yml down

# Pull required images (if internet is available)
echo "📥 Pulling Docker images (if internet is available)..."
if ping -c 1 ******* > /dev/null 2>&1; then
    echo "🌐 Internet connection detected, pulling latest images..."
    $DOCKER_COMPOSE -f docker-compose.yml -f docker-compose.local.yml pull
else
    echo "🔒 No internet connection detected, using local images..."
fi

# Start the infrastructure
echo "🏗️  Starting infrastructure services..."
$DOCKER_COMPOSE -f docker-compose.yml -f docker-compose.local.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Function to check if a port is open
check_port() {
    local host=$1
    local port=$2
    local service=$3
    
    if nc -z $host $port 2>/dev/null; then
        echo "✅ $service is running on $host:$port"
        return 0
    else
        echo "❌ $service is not responding on $host:$port"
        return 1
    fi
}

# Check critical services
check_port $LOCAL_IP 8081 "Traefik (Main Entry Point)"
check_port $LOCAL_IP 35432 "PostgreSQL"
check_port $LOCAL_IP 36379 "Redis"
check_port $LOCAL_IP 36672 "RabbitMQ"
check_port $LOCAL_IP 39000 "MinIO"
check_port $LOCAL_IP 7880 "LiveKit"

echo ""
echo "🎉 Videochat Local Network Setup Complete!"
echo "=========================================="
echo ""
echo "📱 Access the application:"
echo "   Main Application: http://$LOCAL_IP:8081"
echo "   Traefik Dashboard: http://$LOCAL_IP:8080"
echo ""
echo "🔧 Administrative Interfaces:"
echo "   MinIO Console: http://$LOCAL_IP:39001"
echo "   RabbitMQ Management: http://$LOCAL_IP:35672"
echo "   Jaeger Tracing: http://$LOCAL_IP:36686"
echo "   OpenSearch Dashboards: http://$LOCAL_IP:5601"
echo ""
echo "📋 Default Credentials:"
echo "   MinIO: AKIAIOSFODNN7EXAMPLE / wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
echo "   RabbitMQ: videoChat / videoChatPazZw0rd"
echo "   PostgreSQL: postgres / postgresqlPassword"
echo ""
echo "🔗 Share this URL with other devices on your local network:"
echo "   http://$LOCAL_IP:8081"
echo ""
echo "📝 To stop the application:"
echo "   $DOCKER_COMPOSE -f docker-compose.yml -f docker-compose.local.yml down"
echo ""
echo "🔄 To view logs:"
echo "   $DOCKER_COMPOSE -f docker-compose.yml -f docker-compose.local.yml logs -f"
