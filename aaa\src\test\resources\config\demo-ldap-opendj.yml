spring:
  ldap:
    urls: "ldap://localhost:1389"
    username: "cn=Directory Manager"
    password: "password2"

custom.role-mappings:
  ldap:
    - their: "MyGroup"
      our: "ROLE_ADMIN"

custom.ldap:
  resolve-conflicts-strategy: WRITE_NEW_AND_REMOVE_OLD
  auth:
    base: "ou=People,dc=example,dc=com"
    enabled: true
    filter: "cn={0}"
  group:
    base: "dc=example,dc=com"
    filter: "cn={0}"
  attribute-names:
    id: "uid" # name of attribute, which is considered as ldap_id. any id-like attribute, which won't be changed on user rename, it can be number or string
    role: "member"
    email: "mail"
    locked: ""
    username: cn
  password:
    encodingType: ""
    strength: 10

custom:
  schedulers:
    sync-ldap:
      enabled: true
      batch-size: 20
      sync-roles: true
      cron: "*/5 * * * * *"
