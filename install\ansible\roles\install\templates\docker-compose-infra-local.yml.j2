# Local network infrastructure configuration for videochat
# This template is optimized for local network deployment without internet access
version: '3.7'

services:
  traefik:
    image: traefik:v3.4.1
    hostname: traefik
    environment:
      - OTEL_PROPAGATORS=jaeger
      # Disable external connectivity checks
      - TRAEFIK_GLOBAL_CHECKNEWVERSION=false
      - TRAEFIK_GLOBAL_SENDANONYMOUSUSAGE=false
    # Simple HTTP configuration for local network
    command: --configFile=/etc/traefik/traefik-local.yml
    ports:
      # Bind to all interfaces for local network access
      - 0.0.0.0:8081:8081  # Main application port
      - 0.0.0.0:8080:8080  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - {{ dir_prefix }}/traefik:/etc/traefik
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-traefik

  postgresql:
    image: postgres:17.0-alpine3.20
    hostname: postgresql
    ports:
      - 0.0.0.0:35432:5432  # Expose for local network access
    volumes:
      - {{ dir_prefix }}/postgresql/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
      - /mnt/chat-postgresql:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql

  postgresql-citus-coordinator-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-coordinator-1
    ports:
      - 0.0.0.0:45401:5432
    volumes:
      - {{ dir_prefix }}/postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - {{ dir_prefix }}/postgresql-citus/coordinator/docker-entrypoint-initdb.d/003-coordinator-init.sh:/docker-entrypoint-initdb.d/003-coordinator-init.sh:z
      - {{ dir_prefix }}/scripts/wait-for-it.sh:/sbin/wait-for-it.sh:z
      - /mnt/chat-citus-coordinator-1:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-coordinator-1

  postgresql-citus-worker-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-1
    ports:
      - 0.0.0.0:45501:5432
    volumes:
      - {{ dir_prefix }}/postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - /mnt/chat-citus-worker-1:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-worker-1

  postgresql-citus-worker-2:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-2
    ports:
      - 0.0.0.0:45502:5432
    volumes:
      - {{ dir_prefix }}/postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - /mnt/chat-citus-worker-2:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-worker-2

  redis:
    image: valkey/valkey:8.1.0-alpine3.21
    hostname: redis
    ports:
      - 0.0.0.0:36379:6379  # Expose for monitoring
    volumes:
      - redis_data_dir:/data
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-redis

  minio:
    image: bitnami/minio:2024.1.29-debian-11-r0
    hostname: minio
    ports:
      - 0.0.0.0:39000:9000  # S3 API
      - 0.0.0.0:39001:9001  # Web UI
    environment:
      - MINIO_ROOT_USER={{ minio_user }}
      - MINIO_ROOT_PASSWORD={{ minio_password }}
      - MINIO_NOTIFY_AMQP_ENABLE_primary=on
      - MINIO_NOTIFY_AMQP_URL_primary=amqp://videoChat:videoChatPazZw0rd@rabbitmq:5672
      - MINIO_NOTIFY_AMQP_EXCHANGE_primary=minio-events
      - MINIO_NOTIFY_AMQP_EXCHANGE_TYPE_primary=direct
      - MINIO_NOTIFY_AMQP_MANDATORY_primary=off
      - MINIO_NOTIFY_AMQP_DURABLE_primary=on
      - MINIO_NOTIFY_AMQP_NO_WAIT_primary=off
      - MINIO_NOTIFY_AMQP_AUTO_DELETED_primary=off
      - MINIO_NOTIFY_AMQP_DELIVERY_MODE_primary=2
      - MINIO_PROMETHEUS_AUTH_TYPE=public
      # Remove external redirect URL for local network
      - MINIO_BROWSER_REDIRECT_URL=
    volumes:
      - /mnt/chat-minio/data:/bitnami/minio/data:z
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-minio

  rabbitmq:
    image: rabbitmq:4.0.9-management-alpine
    hostname: rabbitmq
    ports:
      - 0.0.0.0:35672:15672  # Management UI
      - 0.0.0.0:36672:5672   # AMQP port
    environment:
      - RABBITMQ_DEFAULT_USER=videoChat
      - RABBITMQ_DEFAULT_PASS=videoChatPazZw0rd
    volumes:
      - rabbitmq_data_dir:/var/lib/rabbitmq/mnesia
      - {{ dir_prefix }}/rabbitmq/additional.conf:/etc/rabbitmq/conf.d/additional.conf:ro,z
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-rabbitmq

  livekit:
    image: livekit/livekit-server:v1.9.0
    command: --config /etc/livekit.yaml
    restart: unless-stopped
    ports:
      # Bind to all interfaces for local network WebRTC
      - "0.0.0.0:7880:7880"      # HTTP API
      - "0.0.0.0:7881:7881"      # TCP RTC
      - "0.0.0.0:7882:7882/udp"  # UDP RTC
    volumes:
      - {{ dir_prefix }}/livekit/livekit.yaml:/etc/livekit.yaml
    networks:
      - backend
    environment:
      - LIVEKIT_CONFIG_FILE=/etc/livekit.yaml
    logging:
      driver: "journald"
      options:
        tag: chat-livekit

  egress:
    image: livekit/egress:v1.9.0
    networks:
      - backend
    environment:
      - EGRESS_CONFIG_FILE=/etc/egress/config.yaml
    volumes:
      - {{ dir_prefix }}/egress/config.yaml:/etc/egress/config.yaml
      - egress_tmp:/home/<USER>/tmp:z
    cap_add:
      - SYS_ADMIN
    logging:
      driver: "journald"
      options:
        tag: chat-egress

  jaeger:
    image: jaegertracing/jaeger:2.7.0
    hostname: jaeger
    ports:
      - 0.0.0.0:34318:4318  # OTLP HTTP
      - 0.0.0.0:34317:4317  # OTLP GRPC
      - 0.0.0.0:36686:16686 # Web UI
    networks:
      - backend
    volumes:
      - {{ dir_prefix }}/jaeger/config.yaml:/jaeger/config.yaml
    environment:
      - JAEGER_LISTEN_HOST=0.0.0.0
    command: [
      "--config",
      "/jaeger/config.yaml",
    ]
    logging:
      driver: "journald"
      options:
        tag: chat-jaeger

  pgadmin:
    image: dcagatay/pwless-pgadmin4:8.13
    hostname: pgadmin
    ports:
      - 0.0.0.0:38080:8080  # Web UI
    networks:
      - backend
    environment:
      - POSTGRES_USER_1=postgres
      - POSTGRES_PASSWORD_1=postgresqlPassword
      - POSTGRES_HOST_1=postgresql
      - POSTGRES_PORT_1=5432
      - POSTGRES_USER_2=postgres
      - POSTGRES_PASSWORD_2=postgresqlPassword
      - POSTGRES_HOST_2=postgresql-citus-coordinator-1
      - POSTGRES_PORT_2=5432
      - PGADMIN_DISABLE_POSTFIX=1
      - PGADMIN_LISTEN_ADDRESS=0.0.0.0
      - PGADMIN_LISTEN_PORT=8080
    logging:
      driver: "journald"
      options:
        tag: chat-pgadmin

  opensearch:
    image: opensearchproject/opensearch:2.18.0
    hostname: opensearch
    ports:
      - 0.0.0.0:9200:9200
    environment:
      - node.name=opensearch
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - DISABLE_SECURITY_PLUGIN=true
      - DISABLE_INSTALL_DEMO_CONFIG=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    networks:
      - backend
    volumes:
      - opensearch_data_dir:/usr/share/opensearch/data
      - {{ dir_prefix }}/opensearch/docker-entrypoint-init.d.sh:/docker-entrypoint-init.d.sh:z
      - {{ dir_prefix }}/opensearch/docker-entrypoint-init.d:/docker-entrypoint-init.d:z
    logging:
      driver: "journald"
      options:
        tag: chat-opensearch
    entrypoint: ["/docker-entrypoint-init.d.sh"]

  dashboards:
    image: opensearchproject/opensearch-dashboards:2.18.0
    hostname: dashboards
    ports:
      - 0.0.0.0:5601:5601
    networks:
      - backend
    environment:
      OPENSEARCH_HOSTS: '["http://opensearch:9200"]'
      DISABLE_SECURITY_DASHBOARDS_PLUGIN: "true"
    logging:
      driver: "journald"
      options:
        tag: chat-opensearch-dashboards

volumes:
  redis_data_dir:
  rabbitmq_data_dir:
  egress_tmp:
  opensearch_data_dir:

networks:
  backend:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.host_binding_ipv4: "0.0.0.0"
    ipam:
      driver: default
      config:
        - subnet: **********/24
