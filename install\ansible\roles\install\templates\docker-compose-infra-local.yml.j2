# Local network infrastructure configuration for videochat
# This template is optimized for local network deployment without internet access
version: '3.7'

services:
  traefik:
    image: traefik:v3.4.1
    hostname: traefik
    environment:
      - OTEL_PROPAGATORS=jaeger
      # Disable external connectivity checks
      - TRAEFIK_GLOBAL_CHECKNEWVERSION=false
      - TRAEFIK_GLOBAL_SENDANONYMOUSUSAGE=false
    # Simple HTTP configuration for local network
    command: --configFile=/etc/traefik/traefik-local.yml
    ports:
      # Main application port
      - target: 8081
        published: 8081
        protocol: tcp
        mode: host
      # Traefik dashboard
      - target: 8080
        published: 8080
        protocol: tcp
        mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./traefik:/etc/traefik
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-traefik

  postgresql:
    image: postgres:17.0-alpine3.20
    hostname: postgresql
    ports:
      - target: 5432
        published: 35432
        protocol: tcp
        mode: host
    volumes:
      - ./postgresql/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
      - /mnt/chat-postgresql:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql

  postgresql-citus-coordinator-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-coordinator-1
    ports:
      - target: 5432
        published: 45401
        protocol: tcp
        mode: host
    volumes:
      - ./postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - ./postgresql-citus/coordinator/docker-entrypoint-initdb.d/003-coordinator-init.sh:/docker-entrypoint-initdb.d/003-coordinator-init.sh:z
      - ./scripts/wait-for-it.sh:/sbin/wait-for-it.sh:z
      - /mnt/chat-citus-coordinator-1:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-coordinator-1

  postgresql-citus-worker-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-1
    ports:
      - target: 5432
        published: 45501
        protocol: tcp
        mode: host
    volumes:
      - ./postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - /mnt/chat-citus-worker-1:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-worker-1

  postgresql-citus-worker-2:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-2
    ports:
      - target: 5432
        published: 45502
        protocol: tcp
        mode: host
    volumes:
      - ./postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - /mnt/chat-citus-worker-2:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-worker-2

  redis:
    image: valkey/valkey:8.1.0-alpine3.21
    hostname: redis
    ports:
      - target: 6379
        published: 36379
        protocol: tcp
        mode: host
    volumes:
      - redis_data_dir:/data
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-redis

  minio:
    image: bitnami/minio:2024.1.29-debian-11-r0
    hostname: minio
    ports:
      - target: 9000
        published: 39000
        protocol: tcp
        mode: host
      - target: 9001
        published: 39001
        protocol: tcp
        mode: host
    environment:
      - MINIO_ROOT_USER={{ minio_user }}
      - MINIO_ROOT_PASSWORD={{ minio_password }}
      - MINIO_NOTIFY_AMQP_ENABLE_primary=on
      - MINIO_NOTIFY_AMQP_URL_primary=amqp://videoChat:videoChatPazZw0rd@rabbitmq:5672
      - MINIO_NOTIFY_AMQP_EXCHANGE_primary=minio-events
      - MINIO_NOTIFY_AMQP_EXCHANGE_TYPE_primary=direct
      - MINIO_NOTIFY_AMQP_MANDATORY_primary=off
      - MINIO_NOTIFY_AMQP_DURABLE_primary=on
      - MINIO_NOTIFY_AMQP_NO_WAIT_primary=off
      - MINIO_NOTIFY_AMQP_AUTO_DELETED_primary=off
      - MINIO_NOTIFY_AMQP_DELIVERY_MODE_primary=2
      - MINIO_PROMETHEUS_AUTH_TYPE=public
      # Remove external redirect URL for local network
      - MINIO_BROWSER_REDIRECT_URL=
    volumes:
      - /mnt/chat-minio/data:/bitnami/minio/data:z
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-minio

  rabbitmq:
    image: rabbitmq:4.0.9-management-alpine
    hostname: rabbitmq
    ports:
      - target: 15672
        published: 35672
        protocol: tcp
        mode: host
      - target: 5672
        published: 36672
        protocol: tcp
        mode: host
    environment:
      - RABBITMQ_DEFAULT_USER=videoChat
      - RABBITMQ_DEFAULT_PASS=videoChatPazZw0rd
    volumes:
      - rabbitmq_data_dir:/var/lib/rabbitmq/mnesia
      - ./rabbitmq/additional.conf:/etc/rabbitmq/conf.d/additional.conf:ro,z
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-rabbitmq

  livekit:
    image: livekit/livekit-server:v1.9.0
    command: --config /etc/livekit.yaml
    ports:
      # HTTP API
      - target: 7880
        published: 7880
        protocol: tcp
        mode: host
      # TCP RTC
      - target: 7881
        published: 7881
        protocol: tcp
        mode: host
      # UDP RTC (CRITICAL for video calls)
      - target: 7882
        published: 7882
        protocol: udp
        mode: host
    volumes:
      - ./livekit/livekit.yaml:/etc/livekit.yaml
    networks:
      - backend
    environment:
      - LIVEKIT_CONFIG_FILE=/etc/livekit.yaml
    deploy:
      restart_policy:
        condition: unless-stopped
    logging:
      driver: "journald"
      options:
        tag: chat-livekit

  egress:
    image: livekit/egress:v1.9.0
    networks:
      - backend
    environment:
      - EGRESS_CONFIG_FILE=/etc/egress/config.yaml
    volumes:
      - ./egress/config.yaml:/etc/egress/config.yaml
      - egress_tmp:/home/<USER>/tmp:z
    cap_add:
      - SYS_ADMIN
    logging:
      driver: "journald"
      options:
        tag: chat-egress

  jaeger:
    image: jaegertracing/jaeger:2.7.0
    hostname: jaeger
    ports:
      - target: 4318
        published: 34318
        protocol: tcp
        mode: host
      - target: 4317
        published: 34317
        protocol: tcp
        mode: host
      - target: 16686
        published: 36686
        protocol: tcp
        mode: host
    networks:
      - backend
    volumes:
      - ./jaeger/config.yaml:/jaeger/config.yaml
    environment:
      - JAEGER_LISTEN_HOST=0.0.0.0
    command: [
      "--config",
      "/jaeger/config.yaml",
    ]
    logging:
      driver: "journald"
      options:
        tag: chat-jaeger

  pgadmin:
    image: dcagatay/pwless-pgadmin4:8.13
    hostname: pgadmin
    ports:
      - target: 8080
        published: 38080
        protocol: tcp
        mode: host
    networks:
      - backend
    environment:
      - POSTGRES_USER_1=postgres
      - POSTGRES_PASSWORD_1=postgresqlPassword
      - POSTGRES_HOST_1=postgresql
      - POSTGRES_PORT_1=5432
      - POSTGRES_USER_2=postgres
      - POSTGRES_PASSWORD_2=postgresqlPassword
      - POSTGRES_HOST_2=postgresql-citus-coordinator-1
      - POSTGRES_PORT_2=5432
      - PGADMIN_DISABLE_POSTFIX=1
      - PGADMIN_LISTEN_ADDRESS=0.0.0.0
      - PGADMIN_LISTEN_PORT=8080
    logging:
      driver: "journald"
      options:
        tag: chat-pgadmin

  opensearch:
    image: opensearchproject/opensearch:2.18.0
    hostname: opensearch
    ports:
      - target: 9200
        published: 9200
        protocol: tcp
        mode: host
    environment:
      - node.name=opensearch
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - DISABLE_SECURITY_PLUGIN=true
      - DISABLE_INSTALL_DEMO_CONFIG=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    networks:
      - backend
    volumes:
      - opensearch_data_dir:/usr/share/opensearch/data
      - ./opensearch/docker-entrypoint-init.d.sh:/docker-entrypoint-init.d.sh:z
      - ./opensearch/docker-entrypoint-init.d:/docker-entrypoint-init.d:z
    logging:
      driver: "journald"
      options:
        tag: chat-opensearch
    entrypoint: ["/docker-entrypoint-init.d.sh"]

  dashboards:
    image: opensearchproject/opensearch-dashboards:2.18.0
    hostname: dashboards
    ports:
      - target: 5601
        published: 5601
        protocol: tcp
        mode: host
    networks:
      - backend
    environment:
      OPENSEARCH_HOSTS: '["http://opensearch:9200"]'
      DISABLE_SECURITY_DASHBOARDS_PLUGIN: "true"
    logging:
      driver: "journald"
      options:
        tag: chat-opensearch-dashboards

volumes:
  redis_data_dir:
  rabbitmq_data_dir:
  egress_tmp:
  opensearch_data_dir:

networks:
  backend:
    driver: overlay
    driver_opts:
      encrypted: "false"
    attachable: true
    ipam:
      driver: default
      config:
        - subnet: **********/24
