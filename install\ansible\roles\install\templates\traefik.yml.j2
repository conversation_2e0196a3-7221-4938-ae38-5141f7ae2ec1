# https://docs.traefik.io/reference/static-configuration/file/
global:
  checkNewVersion: false
  sendAnonymousUsage: false
entryPoints:
  http:
    address: :80
  https:
    address: :443
    transport:
      # configured to be able to upload #GB+ files
      respondingTimeouts:
        readTimeout: 240s
      keepAliveMaxTime: 240s
  traefik:
    address: :8080
providers:
  providersThrottleDuration: 2s
  swarm:
    endpoint: "unix:///var/run/docker.sock"
    watch: true
    exposedByDefault: false
  file:
    directory: /etc/traefik/dynamic

api:
  insecure: true
  dashboard: true
log:
  level: INFO
  format: json
accessLog:
  format: json
  fields:
    defaultMode: keep
    headers:
      defaultMode: drop
      names:
        User-Agent: keep
        uber-trace-id: keep
tracing:
  sampleRate: 1.0
  otlp:
    http:
      endpoint: http://jaeger:4318/v1/traces

certificatesResolvers:
  myresolver:
    acme:
      email: {{ acme_email }}
      storage: /etc/traefik/acme.json
      httpChallenge:
        # used during the challenge
        entryPoint: http
