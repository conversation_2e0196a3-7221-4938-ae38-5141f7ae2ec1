# https://github.com/livekit/livekit/blob/master/config-sample.yaml
port: 7880
rtc:
    #node_ip: {{ ansible_default_ipv4.address }}
    udp_port: 7882
    #tcp_port: 7881
    #tcp_port: 0
    use_external_ip: true
    #use_ice_lite: true
    use_ice_lite: false
    #port_range_start: 35200
    #port_range_end: 35400
    #force_tcp: true
    allow_tcp_fallback: false
    congestion_control:
        enabled: true
        # in the unlikely event of highly congested networks, SFU may choose to pause some tracks
        # in order to allow others to stream smoothly. You can disable this behavior here
        allow_pause: true

room:
#    enabled_codecs:
#        - mime: audio/opus
#        - mime: video/h264
    # limit number of participants that can be in a room, 0 for no limit
    # here several connections behalf the same user are considered as the different participants from Livekit's POV, see GetTokenHandler
    max_participants: {{ livekit_max_participants_per_room }}

turn:
    enabled: true
    udp_port: 3478

redis:
    address: redis:6379
    username: ""
    password: ""
    db: 2
keys:
    {{ livekit_api_key }}: {{ livekit_api_secret }}
logging:
    json: false
    level: info

webhook:
    api_key: '{{ livekit_api_key }}'
    urls:
      - 'http://video:1237/internal/livekit-webhook'

audio:
    active_level: 30
    min_percentile: 50
    smooth_intervals: 2
