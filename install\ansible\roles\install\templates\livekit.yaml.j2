# LiveKit configuration for local network deployment (without internet access)
# https://github.com/livekit/livekit/blob/master/config-sample.yaml
port: 7880
rtc:
    udp_port: 7882
    tcp_port: 7881
    # For local network operation - disable external IP detection
    use_external_ip: false
    # Disable ICE lite for better local network compatibility
    ice_lite: false
    # Use local network interfaces
    node_ip: ""
    allow_tcp_fallback: false
    congestion_control:
        enabled: true
        allow_pause: true

room:
    # Enable common codecs for local network operation
    enabled_codecs:
        - mime: audio/opus
        - mime: video/vp8
        - mime: video/h264
    # limit number of participants that can be in a room, 0 for no limit
    max_participants: {{ livekit_max_participants_per_room }}
    auto_create: true
    # Disable simulcast for simpler local network operation
    enable_simulcast: false

# Disable TURN server for offline operation
turn:
    enabled: false

# Use internal Redis
redis:
    address: redis:6379
    username: ""
    password: ""
    db: 2

# API keys
keys:
    {{ livekit_api_key }}: {{ livekit_api_secret }}

logging:
    json: false
    level: info

# Internal webhook URL - use container networking
webhook:
    api_key: '{{ livekit_api_key }}'
    urls:
      - 'http://video:1237/internal/livekit-webhook'

audio:
    # minimum level to be considered active, 0-127, where 0 is loudest
    # optimized for local network (less sensitive)
    active_level: 100
    min_percentile: 50
    smooth_intervals: 2

# Development mode settings for local network
development: true

# WebRTC configuration for local network - disable external ICE servers
webrtc:
    # Use only local candidates - no external STUN/TURN servers
    ice_servers: []
