# Local Network Deployment Guide

This guide explains how to deploy videochat for local network operation without internet access using the modified Ansible templates.

## 🎯 **Overview**

The local network deployment configuration addresses the key issues you encountered:

1. **LiveKit WebRTC Configuration**: Optimized for local networks without external STUN/TURN servers
2. **Service Binding**: All services bound to `0.0.0.0` for local network access
3. **HTTP-Only Operation**: No HTTPS/TLS requirements
4. **Firewall Configuration**: Proper port opening for local network access
5. **Offline Operation**: No external dependencies

## 🔧 **Key Configuration Changes**

### **LiveKit Configuration (`livekit.yaml.j2`)**
```yaml
# Local network optimizations
use_external_ip: false          # Disable external IP detection
ice_lite: false                 # Better local network compatibility
turn.enabled: false             # Disable TURN server for offline operation
webrtc.ice_servers: []          # No external STUN/TURN servers
development: true               # Enable development mode
```

### **Service Binding**
- All services bound to `0.0.0.0` instead of internal Docker networks
- Direct port exposure for local network access
- No reverse proxy complexity for local deployment

### **Firewall Ports**
The following ports are automatically opened:
- `8081/tcp` - Main application
- `8080/tcp` - Traefik dashboard
- `7880/tcp` - LiveKit HTTP API
- `7881/tcp` - LiveKit TCP RTC
- `7882/udp` - LiveKit UDP RTC (critical for video calls)
- `39000/tcp` - MinIO API
- `39001/tcp` - MinIO Console
- `35672/tcp` - RabbitMQ Management
- `36686/tcp` - Jaeger UI
- `38080/tcp` - PgAdmin
- `5601/tcp` - OpenSearch Dashboards

## 🚀 **Deployment Steps**

### **1. Prepare Variables File**
```bash
cd install/ansible
cp roles/install/vars/local-network.yml.example roles/install/vars/main.yml
```

Edit `roles/install/vars/main.yml` and customize:
```yaml
# Set deployment mode
deployment_mode: "local"

# Configure your network
ansible_default_ipv4:
  address: "*************"  # Your server's IP

# LiveKit configuration
livekit_api_key: "devkey"
livekit_api_secret: "secret"
livekit_max_participants_per_room: 50

# Database passwords
postgresql_password: "postgresqlPassword"
chat_db_password: "chatPazZw0rd"

# MinIO credentials
minio_user: "admin"
minio_password: "password123"
```

### **2. Create Inventory File**
Create `install/ansible/inventory`:
```ini
[videochat]
your-server-ip ansible_user=your-username

[videochat:vars]
ansible_become=yes
ansible_become_method=sudo
```

### **3. Run Local Network Deployment**
```bash
cd install/ansible

# Run the local network playbook
ansible-playbook -i inventory playbook-local-network.yaml --ask-become-pass
```

### **4. Alternative: Use the Deployment Script**
On Linux systems, you can use the automated script:
```bash
cd install/ansible
chmod +x deploy-local-network.sh
./deploy-local-network.sh
```

## 🔍 **Verification Steps**

### **1. Check Service Status**
```bash
docker ps
```
All services should be running.

### **2. Test LiveKit WebRTC**
```bash
curl http://YOUR_SERVER_IP:7880
```
Should return LiveKit server information.

### **3. Test Main Application**
Open browser: `http://YOUR_SERVER_IP:8081`

### **4. Test Video Functionality**
1. Create user accounts
2. Start a video call
3. Check browser console for WebRTC errors

## 🛠️ **Troubleshooting**

### **Video Calls Not Working**

**Problem**: Video calls fail to connect
**Solution**:
1. Verify UDP port 7882 is open: `sudo firewall-cmd --list-ports`
2. Check LiveKit logs: `docker logs videochatstack_livekit_1`
3. Verify local network connectivity between devices
4. Check browser console for WebRTC errors

### **Services Not Accessible from Other Devices**

**Problem**: Can't access from other computers on network
**Solution**:
1. Verify services are bound to `0.0.0.0`: `netstat -tlnp | grep :8081`
2. Check firewall rules: `sudo firewall-cmd --list-all`
3. Test connectivity: `telnet YOUR_SERVER_IP 8081`

### **LiveKit Connection Errors**

**Problem**: WebRTC connection failures
**Solution**:
1. Check LiveKit configuration: `cat /opt/videochat/livekit/livekit.yaml`
2. Verify no external STUN servers: Look for empty `ice_servers: []`
3. Check network routing between devices
4. Ensure all devices are on same network segment

### **Database Connection Issues**

**Problem**: Services can't connect to database
**Solution**:
1. Check PostgreSQL status: `docker logs videochatstack_postgresql_1`
2. Verify database initialization
3. Check connection strings in service logs

## 📊 **Monitoring and Management**

### **Access Management Interfaces**
- **Traefik Dashboard**: `http://YOUR_SERVER_IP:8080`
- **MinIO Console**: `http://YOUR_SERVER_IP:39001` (admin/password123)
- **RabbitMQ Management**: `http://YOUR_SERVER_IP:35672` (videoChat/videoChatPazZw0rd)
- **Jaeger Tracing**: `http://YOUR_SERVER_IP:36686`
- **PgAdmin**: `http://YOUR_SERVER_IP:38080`
- **OpenSearch Dashboards**: `http://YOUR_SERVER_IP:5601`

### **Log Monitoring**
```bash
# View all service logs
docker logs videochatstack_video_1
docker logs videochatstack_livekit_1
docker logs videochatstack_chat_1

# Follow logs in real-time
docker logs -f videochatstack_livekit_1
```

## 🔒 **Security Considerations**

### **Local Network Security**
- Services are bound to `0.0.0.0` for local network access
- No HTTPS/TLS encryption (HTTP only)
- Default passwords should be changed in production
- Firewall rules allow local network subnets

### **Recommended Security Measures**
1. Change default passwords in `main.yml`
2. Restrict firewall rules to specific IP ranges
3. Use VPN for remote access instead of exposing to internet
4. Regular security updates of Docker images

## 📈 **Performance Optimization**

### **Local Network Optimizations**
- LiveKit configured for local network operation
- Reduced WebRTC negotiation complexity
- Direct service access without reverse proxy overhead
- Optimized for LAN bandwidth and latency

### **Resource Requirements**
- **Minimum**: 4GB RAM, 2 CPU cores, 20GB disk
- **Recommended**: 8GB RAM, 4 CPU cores, 50GB disk
- **Network**: Gigabit LAN for HD video calls

## 🆘 **Support and Documentation**

### **Configuration Files Location**
- Main config: `/opt/videochat/`
- LiveKit config: `/opt/videochat/livekit/livekit.yaml`
- Traefik config: `/opt/videochat/traefik/traefik-local.yml`
- Docker Compose: `/opt/videochat/docker-compose-*.yml`

### **Useful Commands**
```bash
# Restart all services
cd /opt/videochat && docker-compose restart

# Update service images
docker-compose pull && docker-compose up -d

# Check service health
docker-compose ps

# View resource usage
docker stats
```

This local network deployment configuration should resolve the LiveKit/WebRTC issues you encountered and provide a fully functional videochat system for local network operation without internet access.
