{"id": "3cd5710f-3484-45a1-871f-2ce0b1571776", "realm": "my_realm2", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "6c3b845a-401d-4611-9cad-66baa52c9d71", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "3cd5710f-3484-45a1-871f-2ce0b1571776", "attributes": {}}, {"id": "2b89a9ed-50e4-4de2-970a-82c025201e0c", "name": "default-roles-my_realm2", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "3cd5710f-3484-45a1-871f-2ce0b1571776", "attributes": {}}, {"id": "f43a57ba-54d1-4f1e-92ef-14bbee8efe44", "name": "USER", "description": "", "composite": false, "clientRole": false, "containerId": "3cd5710f-3484-45a1-871f-2ce0b1571776", "attributes": {}}, {"id": "f02e5b24-97a1-49be-9d62-9c60f18cc22d", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "3cd5710f-3484-45a1-871f-2ce0b1571776", "attributes": {}}], "client": {"realm-management": [{"id": "81893e76-5cc9-4937-a4ee-c58a7419f7ad", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "82aacf07-0f77-46fd-8637-189ed8d85318", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "a45c0046-9536-48fe-a60a-5d3abc02ca52", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "00d7e6ae-a477-43c8-b36c-6da80e937e93", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "a23222e0-f1ae-4e25-86fe-2bc6139da558", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "d100c430-9a2e-4abf-8e91-8ee16797e613", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "d7af83b6-934c-48b5-94a6-94bcae73c2d3", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "02b915c1-5771-4253-aadb-bae5d5b42649", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "421f2ece-b2c2-4871-b7c6-fe3d6b7bc47d", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "21a0a198-1da2-4999-a781-310f0f79c982", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "93d7a544-e61a-4447-83e4-20e41f66036d", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["impersonation", "manage-authorization", "view-authorization", "view-identity-providers", "view-events", "view-clients", "manage-identity-providers", "query-clients", "manage-clients", "manage-users", "manage-realm", "query-realms", "create-client", "view-realm", "query-groups", "manage-events", "query-users", "view-users"]}}, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "9d1502fb-3225-4ca5-974c-c0f0b4e876dc", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "dad4b4b0-fc0c-475d-905d-4effbf3319b7", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "be140be5-681b-4d5d-bf65-83afd2604bfb", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "e1954c23-7001-4696-b25e-37def7788d19", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "e708b27e-52a8-4088-a2cf-10113e7786bd", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "691a51f5-79bf-43ec-8535-9030293c097d", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "3b29ee01-5b1f-43b2-bc37-d5ee784b05b4", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}, {"id": "8a028ced-bec1-46d7-9ec1-f775e0424bab", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "0fe86ccc-f778-4eb9-a318-c7827b1f9b33", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "4c5ab23b-0126-45dd-8af5-c9647c74f7fd", "attributes": {}}], "my_client2": [], "account": [{"id": "a26d4912-8080-41d8-a17f-46b4c165af87", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}, {"id": "34693d96-878a-411c-a5f8-7758f9108465", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}, {"id": "f08a7770-419b-44f7-96d1-c65a819f8eb9", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}, {"id": "f39a8ebd-8261-4160-8a91-c9aed88881b2", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}, {"id": "bd484e90-c196-4e40-8d01-2875af6dc801", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}, {"id": "922fda07-3d4b-4e7b-ae7b-67389c146ec8", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}, {"id": "9099efa5-0ceb-4473-b9b6-59315331f25d", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}, {"id": "935e33ca-d06f-45f4-b9f1-a0467ab67740", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "2b89a9ed-50e4-4de2-970a-82c025201e0c", "name": "default-roles-my_realm2", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "3cd5710f-3484-45a1-871f-2ce0b1571776"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "ac67ec91-ffd5-430e-ad30-eb316036e0c1", "username": "service-account-my_client2", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "my_client2", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-my_realm2"], "clientRoles": {"realm-management": ["view-realm", "view-users"]}, "notBefore": 0, "groups": []}, {"id": "959a4c72-9d37-474b-b718-1d3c9e18a462", "username": "user2", "firstName": "User", "lastName": "Second", "email": "<EMAIL>", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "credentials": [{"id": "fc5fd129-09d3-4f69-a5b5-0c8cfe796110", "type": "password", "userLabel": "My password", "createdDate": *************, "secretData": "{\"value\":\"JYaDBX9n0Pk3E2N3urCJp+2sVYKyM4m7LifagpNi/0k=\",\"salt\":\"iqRbwGkPJGKfS3eY98WT1Q==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":5,\"algorithm\":\"argon2\",\"additionalParameters\":{\"hashLength\":[\"32\"],\"memory\":[\"7168\"],\"type\":[\"id\"],\"version\":[\"1.3\"],\"parallelism\":[\"1\"]}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-my_realm2", "USER"], "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "1d4cca51-6b57-4709-a9ec-092f6bcbe545", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/my_realm2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/my_realm2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "4ce2605f-100b-437f-b319-cc2a98c18ea2", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/my_realm2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/my_realm2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "6eee35c1-4631-452f-9a73-5d3479231b60", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "694d48db-91bb-499f-945e-92dda0df526e", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "4c5ab23b-0126-45dd-8af5-c9647c74f7fd", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "5055ed79-79bc-47f4-9962-fd80225ee70e", "clientId": "my_client2", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "z8cr0Nw2z8c7OpSvEix75GgZeDrWJi60", "redirectUris": ["http://localhost:8081/*", "http://localhost:8060/*", "http://localhost:9080/*"], "webOrigins": ["http://localhost:8060", "http://localhost:9080", "http://localhost:8081"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "cd23d0cf-64ca-4e83-8aef-4d21c51e83ea", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "8c8df935-be10-4898-8498-99c6e2454270", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "ac9ba946-3e50-45e8-b3ef-a689591f8ca6", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "openid", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6f3435eb-c12c-46b1-9420-20dae3a77d24", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "33b718cd-b3af-4cf7-a324-ee9eebb47e28", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/my_realm2/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/my_realm2/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "ebd7ca87-ac52-4512-9842-007367b518f3", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "6f17bd79-a14e-4cec-8abe-4a3f5fdf79db", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "f3a000f6-3b33-4c0e-bcfc-fa0b6b5a7701", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "1510a462-de6f-4480-a8ab-545cfdb592b0", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "d3b9b078-9f40-40b6-9292-66a39f63598e", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "4f7d2b62-473e-4f85-8852-4d762531677c", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "72e6135b-6237-4713-bd39-9ab37ce3f153", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "88ab9951-eadd-4293-99a9-532ead626db4", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "1075c227-943d-49fa-8aaf-4347db021532", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "a598a5d0-2cbc-40e3-a26e-f256d9ea1d40", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "751de928-a70e-4ddb-b74f-918fc983a55f", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "58c31450-ca48-4956-bdf5-b393bce10f1e", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "ec315edf-00de-4f87-95ec-39df3e15132e", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "c673c8f9-aab7-409e-b2d8-6abc29fb8d7a", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "26d0c040-556c-435b-9766-f3e859b8e95b", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "2dfa3a15-059b-4eb4-99c8-80fca0821e92", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "b2b37799-527b-4e48-bace-71b2f2e7eee9", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "b3577e06-bae7-4fbe-93a9-ed6e739526f6", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "178a0748-79ec-43bc-91e4-a9748946855c", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "8e8591a7-b5cf-4e08-b2b0-46dbce859d07", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "b1ba254e-9032-401d-ab78-3ab494fa7cb0", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "15e12346-aa80-47e2-bcf5-d6a0eb64fbe5", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "ecca9d92-3b18-4f14-824a-43762ca4835b", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "3daf2227-ba5c-4e5b-b0db-009a6d34eed1", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "eb12de37-169f-4fb6-8600-2555dc2d84d4", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "2705fda2-8b14-4311-b503-5d6da7905ebe", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "4b22624a-88da-43c9-82f1-a2d9dd96275b", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "d6f082e3-9e70-4a3a-a25c-0551aef33c0b", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "ef8268ae-a79b-4836-ad7c-0036b55f7e4d", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "bd7bf25d-6e8f-4d8c-bdff-6f0428723b16", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "92b9b8e8-d627-4e8e-983a-abec2d259e3f", "name": "openid", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"id": "498a3ced-ee18-4894-8f55-9a8e65286226", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "fe3d83b5-89b9-49e6-9764-b60f9718df3a", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}, {"id": "2eae1bb0-4128-44eb-9b8d-30861d9c0754", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}]}, {"id": "837de4e7-db8d-4259-8533-07a3a285c0b7", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "5d112316-f441-46f0-ab90-50701d2181b0", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "aae18cbf-9c2e-44a3-b8d4-ae2f31e580c0", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "efef9c23-fe0d-4b3d-acd0-4978ffc14f71", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "62552302-0283-48c7-acd6-0c6713090d8c", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "e71a9dce-98d6-4fd6-a8a5-9601a0cc3887", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "00de442a-d19a-4a21-9087-8452c775235b", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "dcb3004a-a8f2-4950-8bc0-d4dbf9cd415b", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr", "basic"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "d83ce885-210d-4408-9141-5397c34de8a7", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "9096f1dd-cbcd-43f5-89b8-be11461ffe5e", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "e7fa048e-9a06-4b2a-b5e2-587fd14c732d", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "36099118-e9cc-421e-b9cc-d653891ef3e5", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "c9e30645-e618-4a83-9ad0-2de69c82337f", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "4e0f4c9b-57db-4711-aaca-e29e10e9a259", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "saml-role-list-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}}, {"id": "2b52ab21-9d4b-49a5-8168-12544fa681d6", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-attribute-mapper", "oidc-address-mapper", "saml-role-list-mapper", "oidc-full-name-mapper"]}}, {"id": "552ecca3-7b0d-4086-ada4-b39a8e8f7d1a", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "ed382b8b-5c3c-49dc-ab1b-8d7ee0cee209", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEA4PzJumkeDQSGH6ATYs5ebRKio2PpyYIUBxM58GXehTuac4KIFmU2M0eKpDgijPW8qyepvoUGGThgKJFC6s2cEa95GYliuBQRlY3E5yKahwPunVc6uTxs/IR0ofTDpy2dTUGWSdQwSCWDkonqf03ZtnecoBxhxBdLC6mxyK04ucyAK+yX/FP/YNt1BZ1dv5jrFCYNmurcwfmXiMCOzY/dyiO9qy9J6wvQIu9V9fHU1jERqytQKxD5nXZ2RkKJ+ljo3M8kjpPiBhx3dIFhCR3CpntHAF4ukmz9sI0oGXTQbZn510K5Ez15JfZCjrkrZKIRkc2z2tCNJ+iWm19vHb3tpwIDAQABAoIBAAEkXSvHgJlBk8+Y/K9fpA9WXf1HJwg3SUe2pCJy2birUndkTLTX0xvfzQWQNdnUou4dLAFw6pqCLsvdZqqxgkyKhvU8jstbNJfGXgfCP8KYqif9b53k39e9Nah0+9akPsUsSsb/0x7JfYvX7KmTF6ZIoG+jzsztGgtVqdZLCYHq4yXdePGx/vmJoQ+eSqXQ6nZie4MB8mWbgkIOH6bxciXEL+o1ZpprOVheYDY7/7DKydUhQpPV2rk3dyN7mDXDCFb9ccvagyes2BrVp/qV3fEkm22NZXxDf3t4p1ZRD7f3yoQrSJAgJ+hG8gJdW1qLLjq5MTClXArwVxOyy8SA1NkCgYEA/f9XtD591Exe/CDOo3caVjoRK4jwVj/3RkzM64WFjkVtBsM3cydsedHaF6B28n7TWR3EjaE1DTbxMPb1tCtM9JhcVWc0KnfUUJV4ld3Eg0PKEjtkYFloLjhaiQYdhedBkcpIX+rBQNuJRyXzFIAa1CRCfZkoEIG39FFHCeGEgh8CgYEA4sLklpbPs4WDRa3iYDwbpZr1DA61HGkG2mgn85djAxikNgyl2TYn91U4ZSzv9033y+753HRPQNbyc3oKKscjwvCZriEYVWp0RkeR8bmXWpnzcQALlzpOSVq4jDrVzWcoILl82HdRp8E7clGNC7slnmnBqmac1CV+OtUWyFhH83kCgYAn7z2a2ThvaJKGkaHrkY5SgE6yyZT/+o6mPuz5GsUk2UWLCqZtNIWZoaTokhIhjk8sc8GzguUUbs79I9b9vNEco9fglZleE/kNsnKDHZsICGB8/VmKzIPUwId7g7qgkvt8MJGwgvhfeiTbStwtHPca45iQpsPl1H9Xqm/A3fQ3gQKBgQCsjJq+kCGaPFUol2kzy2lKOcCPnT1pUe36EdmcvSyFdaG20yb8Qv5kcT+JlYlACN8vGjic7RBFiYpSHqJP7NqkgsqeOua2UFwMAnfmlU+7jHw3F066k45wuQzHABoiw4VAaYS7fwfue9ZNZgrKy2yTv2uzy4M41xLXl8BPUQXnGQKBgQDDVUtCUXUjBf/ye3YVMe8BmLoKu1yWuOTHloweGGqfLrl52Qj+daRlFsy1e8wwyENLvlzU7WA4OOrO3FZcrK8WfoB9nLJM49X+FP+u+8njeV5oqDFVumFX8lEPNK2Q/lHHU/Yb5fZ44P9Y6RicyF0RGXE9SjGQ8Gai8oFGxK8Phw=="], "keyUse": ["SIG"], "certificate": ["MIICoTCCAYkCBgGRxfd6LjANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlteV9yZWFsbTIwHhcNMjQwOTA2MDYxMTU1WhcNMzQwOTA2MDYxMzM1WjAUMRIwEAYDVQQDDAlteV9yZWFsbTIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDg/Mm6aR4NBIYfoBNizl5tEqKjY+nJghQHEznwZd6FO5pzgogWZTYzR4qkOCKM9byrJ6m+hQYZOGAokULqzZwRr3kZiWK4FBGVjcTnIpqHA+6dVzq5PGz8hHSh9MOnLZ1NQZZJ1DBIJYOSiep/Tdm2d5ygHGHEF0sLqbHIrTi5zIAr7Jf8U/9g23UFnV2/mOsUJg2a6tzB+ZeIwI7Nj93KI72rL0nrC9Ai71X18dTWMRGrK1ArEPmddnZGQon6WOjczySOk+IGHHd0gWEJHcKme0cAXi6SbP2wjSgZdNBtmfnXQrkTPXkl9kKOuStkohGRzbPa0I0n6JabX28dve2nAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAJu7GiW6fZLrs0mfBuFYMrw/HGcIgXuXgwoOFhmJ6uNofy+YE4tmrq1+cF2ZseaCqL4gTsOVoBWQ82lKM356Fy5P/SUE0aZ9wtuU3J/Bw4DFlrtYhorAbPPGsnj9LUXVEgT6johDCN6Cm3U++dQEMCPuBuFDdC395oNR1wrW6ziSJ7iHt/L1eH575ht4S+e2ysrjpqrbyF8ikQhU4pTdZlw7WTcSmTAE8PGK59NiPezy5PMduLZwWnF0oJ7rhdkVjeeEwdTOKpRLUiXRXQn76brg9bqreZldWGwVqenegqzrkxKnpn43rpVoukYfo2AGNZAMqfEM5jjWszN+PImw1FE="], "priority": ["100"]}}, {"id": "6d6694cc-f259-4074-bc3f-3be278b56832", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["784a8faf-ef06-4bd7-8f65-2ccdc40307bd"], "secret": ["ETgxWnxvbgShi-Jl7gMgFuwwveIATd-Qd8w09TkBogyKn2Ab1_W8SyyVYeFF6n6Abma9dNmQNYSiZNw2tAzzLVAP2EhoMk09S_KuxRFlCdGRlizzoYOCezbk_VuxVqoWl6yC1yoYqi6NtaYu0OpEMC9v-Gelq5AQf-xKMYzf-Eg"], "priority": ["100"], "algorithm": ["HS512"]}}, {"id": "c1e9f963-6a39-49b2-9b95-184e46c4bbaa", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAr8x/OUdecDkaWGQlO9X74oGCWGfgNAqK7r/HuUBI5mvhfkQBI+75nyxurMtbu95/px9h6BewUIVKJ+syjVrvhzrelKsEpIEMgCUzGW3JhkgOXKBQS2ZXylORYJmQfSmVH+A0D6pxNJlUZAE5qCYOjr0bB6Vcbtse5DpnMitdrCW/llb1LO4bpH/i9DEi7ApAnwlNsptbdMQxER7DasxC/JJzS5GWXZj0Ig7bEeyhR/IBFyNcFeDqgNo8sgcTb8gZcDSrnPLEg4OPdw9/D7IKOYYXZtISAOYLaZmwFtoMDF2qEuYWuIMO/9QELlOxtNVlmZnzocsHU3MMrz/lZhd/dwIDAQABAoIBAAsCkzs6oYsEwIYlq4FOWwqqEshHpS6NNHtUboVHJemsewBl8O5XsrGJ3lqbF+Exw6vrVr3q8eNxHZ5XaCCbP+InyTXlahKFdIt4CPXAPv1WePPLJIw8WiY1SqcSpw53sru+1nPeuF1TdwoWW46Q55t/83fsntfgiFRB5iGpvqGf1u5CBypVdaFEQrf/BtZAnhdUcsF8cRCLN6eyp/5VnbHLHzkdOHsMY/DRyVC9mL5fxA1vF8hmy0byNQDtUp1Uf40wIYxCZ9Jm5AlwZcec6A6NGLCBcJnZZhuJUb4BCtgP7CEc1KZjxDq0TDZQw8rs1369yHrST/09uDleMCyicxECgYEA51OnjVhKRFAsRc4ud9LQYXHXlqQcoEHrPw+9jBqnHnmPXr/GZyvMrcPsOfc8qsddMlyZZ+BNewNwJO/rnbbaWA+OfKeTzoTnC3PNzSlrV15Gz6ZiixPAd+C8P66Ep2DHaIzrEziRCDoCIFBmNChZT0ymP89LnH7XyrtzWOGGq7ECgYEAwoyoxJWTWKv+ynHWdUFdRt7ypy/5yQO9dWzrGv9WUexWEDJTnTMNq8zkKCKFNXeZbXMFnwro3z4z7VZ/Z/XLnWjG8cegAiM9p2Mvx+pphaPyNNjYfXWNHTjBKvgMfdSMO8h4VLlE6NYwws2ufUmVehacHs/8E6ISwwXUX684L6cCgYBFJL8PQ5WnR6OEFfXTwQKiLcn38APSbwGm7siESS8sr78H3iCqk+/w/H/ub5UFouObewwrfWMLYuKxV7lAgfhPnA7F+bz3XMhGpw2bxkacbcSSqrQHyBaefmEmU7643Pedq4lUTHMlV1ltWTrDWlAfoNSMfCSomU1c/pyis35ycQKBgHOoTXi492Tis1FFBdqlZj+IsTcFRXjkOVDC1K4zF+0SFEQCt0SgIkN9RjI5+3lCxbqUg8X+JVz6TMKPBqUWMds9e/Am3hAHRVLqm3A9AkiJo6cZNvK23JJ/uA7EciCoKEouLqMnlTjahRh6UH55LVzsbhm/SbueQYshPxU3RK7HAoGBAI3Zh21jNz2CQKDh91FsmZJ+ON0ZQWu53l9KL9oLk6awATy/p7+ZnNhyUiCJNBryIsFmomS5v2B/crBNZXr7r6hC/72FqGzQndpJ8/fLZGHsmm1wdbji4W7EcoEg2RfkwK6tVdTaM/rBEWsQRH7yxlFO+i10ZzwUcTDfKC7EdY3E"], "keyUse": ["ENC"], "certificate": ["MIICoTCCAYkCBgGRxfd6tTANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlteV9yZWFsbTIwHhcNMjQwOTA2MDYxMTU2WhcNMzQwOTA2MDYxMzM2WjAUMRIwEAYDVQQDDAlteV9yZWFsbTIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCvzH85R15wORpYZCU71fvigYJYZ+A0Coruv8e5QEjma+F+RAEj7vmfLG6sy1u73n+nH2HoF7BQhUon6zKNWu+HOt6UqwSkgQyAJTMZbcmGSA5coFBLZlfKU5FgmZB9KZUf4DQPqnE0mVRkATmoJg6OvRsHpVxu2x7kOmcyK12sJb+WVvUs7hukf+L0MSLsCkCfCU2ym1t0xDERHsNqzEL8knNLkZZdmPQiDtsR7KFH8gEXI1wV4OqA2jyyBxNvyBlwNKuc8sSDg493D38Psgo5hhdm0hIA5gtpmbAW2gwMXaoS5ha4gw7/1AQuU7G01WWZmfOhywdTcwyvP+VmF393AgMBAAEwDQYJKoZIhvcNAQELBQADggEBABPrbHHJC/2FJ+TFPaTn+pabHm+fD7K8c76gAouaZRII7z6QK3REv+MTiruLeyEDKKm7cgnZ/SWVK+Ep1K2sgDkDz8/ZpvCYeSvcLYK8/4Vm1LBFIPc9a6oI7Qw2/q0IwbCkPt+/A3ZBTsl5yZ2mZaYaF8SSN/X1TLn7QF9eRMuVWwazppECu8pxGlSfxme/ZNRt8CETsOsSPpbiUxbf6EaGjh9z4khMD8XnW6ZrkQOo9STmfFYper+vhwNGNy85vo+LfnQ7nDpb20DK/rgMxXT+16dNAwrQi8VrgC/0jLJoFwPqlQ+rubBtkghIIg0JcTj8GTh4GpdiC9JgiqCLkeQ="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "43d8610e-bfc1-4001-9318-738f28796c3b", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["72d22d21-cbf5-4ede-8c7d-ed5936a42392"], "secret": ["rkqWwf85CG6u8trIs3CGIA"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "94111c6c-725e-4576-acca-5110f77ebf47", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "d87f7a1c-1bc0-4296-ac94-9a878b349d20", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e423bfee-605c-407a-946c-f0d29e28459b", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "b26fa86d-2053-4679-8d1d-94ad9c54dabb", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a0051e17-fb31-4a10-9d96-9d18d05119db", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "********-8c3f-4486-8c93-85bd5569ebe8", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "00417d7a-02f8-40b4-88bb-0492d1c4a178", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "d2f9518e-c18a-46a2-afd7-4cb2dfa9470c", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "a3ff437e-6422-4521-a979-d35154477284", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "5a517ef7-e217-44a6-86d0-695b131817e2", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "24de8cd3-8db3-42c6-9213-46e19941bb1d", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "664b1163-6096-4ade-9861-66862ea92993", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "f76d77e4-aa9e-4818-9826-5cc9b19100ba", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "766e728d-a492-4aab-83b2-41945530bbe7", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "ee8da22a-5fec-47f3-a50a-292fce085c3a", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "62bbdea1-8e31-42a2-9258-78c615318680", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 70, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "03dc6c56-cd8e-415d-87b5-764d4a188f04", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "b62751ee-44f2-4677-bb36-e27f587b10ba", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "22ef188e-31b5-4f02-b5e7-f8a3e18e5d10", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "e7a1a804-b701-416d-b3fd-9b890f7922e3", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": true, "defaultAction": false, "priority": 90, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaAuthRequestedUserHint": "login_hint", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5", "realmReusableOtpCode": "false", "cibaExpiresIn": "120", "oauth2DeviceCodeLifespan": "600", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "organizationsEnabled": "false"}, "keycloakVersion": "25.0.1", "userManagedAccessAllowed": false, "organizationsEnabled": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}