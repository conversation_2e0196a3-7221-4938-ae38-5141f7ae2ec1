---
# Ansible playbook for local network deployment of videochat
# This playbook is optimized for deployment without internet access

- name: Deploy videochat for local network
  hosts: all
  become: yes
  vars:
    deployment_mode: "local"
  
  pre_tasks:
    - name: Check if deployment mode is set to local
      assert:
        that:
          - deployment_mode == "local"
        fail_msg: "This playbook is only for local network deployment. Set deployment_mode to 'local'."
    
    - name: Display local network deployment warning
      debug:
        msg: |
          ========================================
          LOCAL NETWORK DEPLOYMENT MODE
          ========================================
          This deployment is optimized for local networks without internet access.
          Features disabled:
          - HTTPS/TLS certificates
          - External STUN/TURN servers
          - External OAuth providers
          - Let's Encrypt certificates
          - External API integrations
          ========================================

  roles:
    - role: install
      vars:
        # Override templates for local network deployment
        livekit_template: "livekit.yaml.j2"
        docker_compose_infra_template: "docker-compose-infra-local.yml.j2"
        docker_compose_video_template: "docker-compose-video-local.yml.j2"
        traefik_template: "traefik-local.yml.j2"
        
        # Local network specific variables
        use_local_network_config: true
        disable_external_dependencies: true
        use_http_only: true
        bind_all_interfaces: true
        
        # LiveKit local network configuration
        livekit_use_external_ip: false
        livekit_ice_lite: false
        livekit_turn_enabled: false
        livekit_development_mode: true
        
        # Service binding configuration
        service_bind_address: "0.0.0.0"
        
        # Disable external integrations
        oauth2_google_enabled: false
        oauth2_facebook_enabled: false
        oauth2_vkontakte_enabled: false
        oauth2_keycloak_enabled: false
        email_enabled: false
        ldap_enabled: false
        
        # SSL/TLS configuration
        ssl_enabled: false
        letsencrypt_enabled: false
        
        # Monitoring configuration
        jaeger_enabled: true
        opensearch_enabled: true
        prometheus_enabled: false

  post_tasks:
    - name: Display local network access information
      debug:
        msg: |
          ========================================
          LOCAL NETWORK DEPLOYMENT COMPLETE
          ========================================
          Access the application at:
          - Main Application: http://{{ ansible_default_ipv4.address }}:8081
          - Traefik Dashboard: http://{{ ansible_default_ipv4.address }}:8080
          - MinIO Console: http://{{ ansible_default_ipv4.address }}:39001
          - RabbitMQ Management: http://{{ ansible_default_ipv4.address }}:35672
          - Jaeger UI: http://{{ ansible_default_ipv4.address }}:36686
          - PgAdmin: http://{{ ansible_default_ipv4.address }}:38080
          - OpenSearch Dashboards: http://{{ ansible_default_ipv4.address }}:5601
          
          LiveKit WebRTC Ports:
          - HTTP API: {{ ansible_default_ipv4.address }}:7880
          - TCP RTC: {{ ansible_default_ipv4.address }}:7881
          - UDP RTC: {{ ansible_default_ipv4.address }}:7882
          
          Database Access:
          - PostgreSQL: {{ ansible_default_ipv4.address }}:35432
          - Redis: {{ ansible_default_ipv4.address }}:36379
          
          Note: All services are bound to 0.0.0.0 for local network access.
          Make sure your firewall allows access to these ports.
          ========================================
    
    - name: Create local network configuration summary
      copy:
        content: |
          # Local Network Deployment Summary
          # Generated on {{ ansible_date_time.iso8601 }}
          
          ## Access URLs
          - Main Application: http://{{ ansible_default_ipv4.address }}:8081
          - Traefik Dashboard: http://{{ ansible_default_ipv4.address }}:8080
          - MinIO Console: http://{{ ansible_default_ipv4.address }}:39001 (admin/{{ minio_password }})
          - RabbitMQ Management: http://{{ ansible_default_ipv4.address }}:35672 (videoChat/videoChatPazZw0rd)
          - Jaeger UI: http://{{ ansible_default_ipv4.address }}:36686
          - PgAdmin: http://{{ ansible_default_ipv4.address }}:38080
          - OpenSearch Dashboards: http://{{ ansible_default_ipv4.address }}:5601
          
          ## LiveKit Configuration
          - WebRTC Server: {{ ansible_default_ipv4.address }}:7880
          - API Key: {{ livekit_api_key }}
          - API Secret: {{ livekit_api_secret }}
          - External IP: Disabled (local network mode)
          - TURN Server: Disabled (offline mode)
          - ICE Servers: None (local network only)
          
          ## Network Configuration
          - Deployment Mode: Local Network
          - HTTPS: Disabled
          - External Dependencies: Disabled
          - Service Binding: 0.0.0.0 (all interfaces)
          
          ## Firewall Ports to Open
          - 8081/tcp (Main application)
          - 8080/tcp (Traefik dashboard)
          - 7880/tcp (LiveKit HTTP)
          - 7881/tcp (LiveKit TCP RTC)
          - 7882/udp (LiveKit UDP RTC)
          - 39000/tcp (MinIO API)
          - 39001/tcp (MinIO Console)
          - 35672/tcp (RabbitMQ Management)
          - 36686/tcp (Jaeger UI)
          - 38080/tcp (PgAdmin)
          - 5601/tcp (OpenSearch Dashboards)
          
          ## Troubleshooting
          If video calls don't work:
          1. Check that UDP port 7882 is open
          2. Verify LiveKit is running: curl http://{{ ansible_default_ipv4.address }}:7880
          3. Check browser console for WebRTC errors
          4. Ensure all devices are on the same network segment
        dest: "{{ dir_prefix }}/LOCAL_NETWORK_DEPLOYMENT.md"
        mode: '0644'
    
    - name: Display final instructions
      debug:
        msg: |
          ========================================
          DEPLOYMENT COMPLETE!
          ========================================
          
          1. Open your browser and go to: http://{{ ansible_default_ipv4.address }}:8081
          2. Create an admin account
          3. Start using videochat on your local network!
          
          Configuration details saved to: {{ dir_prefix }}/LOCAL_NETWORK_DEPLOYMENT.md
          
          For troubleshooting, check the logs:
          docker logs {{ swarm_stack_name | lower }}_livekit_1
          docker logs {{ swarm_stack_name | lower }}_video_1
          ========================================
