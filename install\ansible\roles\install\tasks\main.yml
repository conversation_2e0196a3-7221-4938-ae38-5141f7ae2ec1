# Local network deployment detection
- name: Check if local network deployment
  set_fact:
    is_local_network: "{{ deployment_mode is defined and deployment_mode == 'local' }}"
  tags:
    - always

- name: Display deployment mode
  debug:
    msg: "Deployment mode: {{ 'Local Network' if is_local_network else 'Cloud/Internet' }}"
  tags:
    - always

- name: Secure sshd
  ansible.builtin.copy:
    src: files/ssh-secure.conf
    dest: /etc/ssh/sshd_config.d/00-secure.conf
    owner: root
    group: root
    mode: 0600
  notify: Reload SSH
  tags:
    - services

- name: Ensure Firewall is running
  service:
    name: firewalld
    state: started
    enabled: yes
  tags:
    - services

- name: Ensure Rsyslog is not running
  service:
    name: rsyslog
    state: stopped
    enabled: no
  tags:
    - services


# # https://www.digitalocean.com/community/tutorial-series/how-to-write-ansible-playbooks
# - name: Docker repo
#   get_url:
#     url: "{{ docker_repo }}"
#     dest: /etc/yum.repos.d/docker-ce.repo
#   tags:
#     - videochat

# # https://docs.rockylinux.org/gemstones/containers/docker/
# - name: Install Docker
#   package:
#     name:
#       - docker-ce
#       - docker-ce-cli
#       - containerd.io
#       - docker-compose-plugin
#     state: present
#   tags:
#     - videochat

- name: Install docker proxy
  when: docker_proxy is defined
  template:
    src: templates/daemon.json.j2
    dest: /etc/docker/daemon.json
  notify: Reload docker
  tags:
    - videochat

- name: Start Docker service
  service:
    name: docker
    state: started
    enabled: yes
  tags:
    - videochat

- name: Create root config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}"
    state: directory
  tags:
    - videochat

- name: Create Livekit config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/livekit"
    state: directory
  tags:
    - videochat

# https://www.digitalocean.com/community/tutorials/how-to-create-and-use-templates-in-ansible-playbooks
- name: Create Livekit config
  template:
    src: templates/livekit.yaml.j2
    dest: "{{ dir_prefix }}/livekit/livekit.yaml"
  tags:
    - videochat

- name: Create FluentBit config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/fluent-bit/etc"
    state: directory
  tags:
    - videochat

- name: Create FluentBit socket dir
  ansible.builtin.file:
    path: "{{ dir_prefix }}/fluent-bit/var/run"
    state: directory
  tags:
    - videochat

- name: Create FluentBit config
  ansible.builtin.copy:
    src: files/fluent-bit.conf
    dest: "{{ dir_prefix }}/fluent-bit/etc/fluent-bit.conf"
  tags:
    - videochat

- name: Create FluentBit parsers config
  ansible.builtin.copy:
    src: files/parsers.conf
    dest: "{{ dir_prefix }}/fluent-bit/etc/parsers.conf"
  tags:
    - videochat

- name: Create Jaeger config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/jaeger"
    state: directory
  tags:
    - videochat

- name: Create Jaeger config
  ansible.builtin.copy:
    src: files/config.yaml
    dest: "{{ dir_prefix }}/jaeger/config.yaml"
  tags:
    - videochat

- name: Create Opensearch config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/opensearch/docker-entrypoint-init.d"
    state: directory
  tags:
    - videochat

- name: Create Opensearch init script
  template:
    src: templates/init.sh.j2
    dest: "{{ dir_prefix }}/opensearch/docker-entrypoint-init.d/init.sh"
  tags:
    - videochat

- name: Create Opensearch entrypoint
  ansible.builtin.copy:
    src: files/docker-entrypoint-init.d.sh
    dest: "{{ dir_prefix }}/opensearch/docker-entrypoint-init.d.sh"
    mode: a+x
  tags:
    - videochat

- name: Create Traefik config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/traefik/dynamic"
    state: directory
  tags:
    - videochat

- name: Create Traefik static config
  template:
    src: templates/traefik-local.yml.j2 #traefik.yml.j2
    dest: "{{ dir_prefix }}/traefik/traefik.yml"
  tags:
    - videochat

- name: Create Traefik dynamic config
  template:
    src: templates/common_middlewares.yml.j2
    dest: "{{ dir_prefix }}/traefik/dynamic/common_middlewares.yml"
  tags:
    - videochat

- name: Create PostgreSQL config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/postgresql/docker-entrypoint-initdb.d"
    state: directory
  tags:
    - videochat

- name: Create PostgreSQL init.sql
  ansible.builtin.copy:
    src: files/init.sql
    dest: "{{ dir_prefix }}/postgresql/docker-entrypoint-initdb.d/init.sql"
  tags:
    - videochat

- name: Create PostgreSQL Citus common config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/postgresql-citus/common/docker-entrypoint-initdb.d"
    state: directory
  tags:
    - videochat
- name: Create PostgreSQL Citus coordinator config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/postgresql-citus/coordinator/docker-entrypoint-initdb.d"
    state: directory
  tags:
    - videochat
- name: Create PostgreSQL Citus 002-common-init.sql
  ansible.builtin.copy:
    src: files/002-common-init.sql
    dest: "{{ dir_prefix }}/postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql"
  tags:
    - videochat
- name: Create PostgreSQL Citus coordinator 003-coordinator-init.sh
  ansible.builtin.copy:
    src: files/003-coordinator-init.sh
    dest: "{{ dir_prefix }}/postgresql-citus/coordinator/docker-entrypoint-initdb.d/003-coordinator-init.sh"
  tags:
    - videochat

- name: Create scripts directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/scripts"
    state: directory
  tags:
    - videochat
- name: Create wait-for-it
  ansible.builtin.copy:
    src: files/wait-for-it.sh
    dest: "{{ dir_prefix }}/scripts/wait-for-it.sh"
    mode: a+x
  tags:
    - videochat


- name: Create RabbitMQ config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/rabbitmq"
    state: directory
  tags:
    - videochat

- name: Create RabbitMQ additional.conf
  ansible.builtin.copy:
    src: files/additional.conf
    dest: "{{ dir_prefix }}/rabbitmq/additional.conf"
  tags:
    - videochat

- name: Create Egress config directory if it does not exist
  ansible.builtin.file:
    path: "{{ dir_prefix }}/egress"
    state: directory
  tags:
    - videochat

- name: Create Egress config
  template:
    src: templates/egress.yaml.j2
    dest: "{{ dir_prefix }}/egress/config.yaml"
  tags:
    - videochat

- name: Create Minio data directory and set owner and access on Minio data directory non recursively
  ansible.builtin.file:
    path: /mnt/chat-minio/data
    state: directory
    mode: u=rwx,g-rwx,o-rwx
    # https://docs.bitnami.com/kubernetes/faq/configuration/use-non-root/
    # https://github.com/bitnami/containers/blob/main/bitnami/minio/2024/debian-12/Dockerfile#L61C6-L61C10
    owner: 1001
    group: 1001
  tags:
    - videochat

- name: Create Storage tmp directory if it does not exist
  ansible.builtin.file:
    path: /mnt/chat-storage-tmp
    state: directory
    mode: a=rw
  tags:
    - videochat

- name: Create PostgreSQL data directory if it does not exist
  ansible.builtin.file:
    path: /mnt/chat-postgresql
    state: directory
    mode: u=rwx,g-rwx,o-rwx
    owner: 70
    group: 70
  tags:
    - videochat

- name: Create PostgreSQL Citus Coordinator 1 data directory if it does not exist
  ansible.builtin.file:
    path: /mnt/chat-citus-coordinator-1
    state: directory
    mode: u=rwx,g-rwx,o-rwx
    owner: 70
    group: 70
  tags:
    - videochat
- name: Create PostgreSQL Citus Worker 1 data directory if it does not exist
  ansible.builtin.file:
    path: /mnt/chat-citus-worker-1
    state: directory
    mode: u=rwx,g-rwx,o-rwx
    owner: 70
    group: 70
  tags:
    - videochat
- name: Create PostgreSQL Citus Worker 2 data directory if it does not exist
  ansible.builtin.file:
    path: /mnt/chat-citus-worker-2
    state: directory
    mode: u=rwx,g-rwx,o-rwx
    owner: 70
    group: 70
  tags:
    - videochat


- name: Create Docker Compose Infra
  template:
    src: templates/docker-compose-infra-local.yml.j2
    # src: templates/docker-compose-infra.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-infra.yml"
  tags:
    - videochat

- name: Create Docker prune cron
  ansible.builtin.copy:
    src: files/docker-prune.sh
    dest: "/etc/cron.daily/docker-prune.sh"
    mode: a+x
  tags:
    - videochat

- name: Install python package
  package:
    name:
      - python
      - pip
    state: present
  tags:
    - videochat

- name: Install python packages to manipulate with docker swarm
  ansible.builtin.pip:
    name:
      - docker
      - jsondiff
  tags:
    - videochat

# https://docs.ansible.com/ansible/latest/collections/community/docker/docker_swarm_module.html#ansible-collections-community-docker-docker-swarm-module
- name: Init a new swarm with default parameters
  community.docker.docker_swarm:
    state: present
  register: swarm_facts
  tags:
    - videochat

- debug:
    msg: "Swarm tokens are {{ swarm_facts.swarm_facts.JoinTokens }}"
  tags:
    - videochat

# https://docs.ansible.com/ansible/latest/collections/community/docker/index.html
- name: Deploy stack for Infra
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-infra.yml"
  tags:
    - videochat

# https://docs.ansible.com/ansible/latest/collections/ansible/builtin/password_hash_filter.html
- name: Create Docker Compose AAA
  template:
    src: templates/docker-compose-aaa.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-aaa.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for AAA
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-aaa.yml"
  tags:
    - videochat

- name: Create Docker Compose Frontend
  template:
    src: templates/docker-compose-frontend.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-frontend.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for Frontend
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-frontend.yml"
  tags:
    - videochat

- name: Create Docker Compose Chat
  template:
    src: templates/docker-compose-chat.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-chat.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for Chat
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-chat.yml"
  tags:
    - videochat

- name: Create Docker Compose Storage
  template:
    src: templates/docker-compose-storage.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-storage.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for Storage
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-storage.yml"
  tags:
    - videochat

- name: Create Docker Compose Video
  template:
    src: templates/docker-compose-video-local.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-video.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for Video
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-video.yml"
  tags:
    - videochat

- name: Create Docker Compose Event
  template:
    src: templates/docker-compose-event.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-event.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for Event
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-event.yml"
  tags:
    - videochat

- name: Create Docker Compose Notification
  template:
    src: templates/docker-compose-notification.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-notification.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for Notification
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-notification.yml"
  tags:
    - videochat

- name: Create Docker Compose Public
  template:
    src: templates/docker-compose-public.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-public.yml"
  vars:
    tag: "{{ image_install_tag }}"
  tags:
    - videochat

- name: Deploy stack for Public
  community.docker.docker_stack:
    state: present
    name: "{{ swarm_stack_name }}"
    compose:
      - "{{ dir_prefix }}/docker-compose-public.yml"
  tags:
    - videochat

- name: Set ssh pub key for Github Actions
  ansible.posix.authorized_key:
    user: "{{ ansible_user }}"
    state: present
    key: "{{ github_actions_ssh_public_key }}"
  tags:
    - continuous

# rest part is set non-deployed tag for deploying without docker hub
- name: Change Docker Compose AAA
  template:
    src: templates/docker-compose-aaa.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-aaa.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

- name: Change Docker Compose Frontend
  template:
    src: templates/docker-compose-frontend.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-frontend.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

- name: Change Docker Compose Chat
  template:
    src: templates/docker-compose-chat.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-chat.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

- name: Change Docker Compose Storage
  template:
    src: templates/docker-compose-storage.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-storage.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

- name: Change Docker Compose Video
  template:
    src: templates/docker-compose-video.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-video.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

- name: Change Docker Compose Event
  template:
    src: templates/docker-compose-event.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-event.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

- name: Change Docker Compose Notification
  template:
    src: templates/docker-compose-notification.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-notification.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

- name: Change Docker Compose Public
  template:
    src: templates/docker-compose-public.yml.j2
    dest: "{{ dir_prefix }}/docker-compose-public.yml"
  vars:
    tag: "{{ image_continuous_tag }}"
  tags:
    - continuous

# Local network specific configuration
# - name: Configure firewall for local network deployment
#   when: is_local_network | default(false)
#   block:
#     - name: Open local network ports in firewall
#       firewalld:
#         port: "{{ item }}"
#         permanent: yes
#         state: enabled
#         immediate: yes
#       loop:
#         - "8081/tcp"    # Main application
#         - "8080/tcp"    # Traefik dashboard
#         - "7880/tcp"    # LiveKit HTTP
#         - "7881/tcp"    # LiveKit TCP RTC
#         - "7882/udp"    # LiveKit UDP RTC
#         - "39000/tcp"   # MinIO API
#         - "39001/tcp"   # MinIO Console
#         - "35672/tcp"   # RabbitMQ Management
#         - "36672/tcp"   # RabbitMQ AMQP
#         - "36379/tcp"   # Redis
#         - "35432/tcp"   # PostgreSQL
#         - "36686/tcp"   # Jaeger UI
#         - "34318/tcp"   # Jaeger OTLP HTTP
#         - "34317/tcp"   # Jaeger OTLP GRPC
#         - "38080/tcp"   # PgAdmin
#         - "9200/tcp"    # OpenSearch
#         - "5601/tcp"    # OpenSearch Dashboards
#       tags:
#         - firewall
#         - local-network

#     - name: Allow local network subnet access
#       firewalld:
#         source: "{{ item }}"
#         zone: trusted
#         permanent: yes
#         state: enabled
#         immediate: yes
#       loop:
#         - "192.168.0.0/16"
#         - "10.0.0.0/8"
#         - "172.16.0.0/12"
#       tags:
#         - firewall
#         - local-network

# - name: Use local network templates when in local mode
#   when: is_local_network | default(false)
#   block:
#     - name: Create LiveKit config for local network
#       template:
#         src: templates/livekit.yaml.j2
#         dest: "{{ dir_prefix }}/livekit/livekit.yaml"
#       tags:
#         - local-network
#         - livekit

#     - name: Create Traefik config for local network
#       template:
#         src: templates/traefik-local.yml.j2
#         dest: "{{ dir_prefix }}/traefik/traefik-local.yml"
#       tags:
#         - local-network
#         - traefik

#     - name: Create Docker Compose infrastructure for local network
#       template:
#         src: templates/docker-compose-infra-local.yml.j2
#         dest: "{{ dir_prefix }}/docker-compose-infra.yml"
#       tags:
#         - local-network
#         - docker-compose

#     - name: Create Docker Compose video service for local network
#       template:
#         src: templates/docker-compose-video-local.yml.j2
#         dest: "{{ dir_prefix }}/docker-compose-video.yml"
#       tags:
#         - local-network
#         - docker-compose

# - name: Display local network deployment information
#   when: is_local_network | default(false)
#   debug:
#     msg: |
#       ========================================
#       LOCAL NETWORK CONFIGURATION APPLIED
#       ========================================
#       The following optimizations have been applied for local network deployment:

#       1. LiveKit configured for local network operation:
#          - External IP detection disabled
#          - TURN server disabled
#          - ICE servers list cleared
#          - Development mode enabled

#       2. Services bound to 0.0.0.0 for local network access

#       3. Firewall ports opened for local network access

#       4. HTTPS/TLS disabled (HTTP-only operation)

#       5. External dependencies disabled

#       Access URLs will be available at: http://{{ ansible_default_ipv4.address }}:8081
#       ========================================
#   tags:
#     - local-network
